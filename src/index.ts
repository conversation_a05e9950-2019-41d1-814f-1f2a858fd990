/*
 * @Author: tony10 <EMAIL>
 * @Date: 2025-06-06 13:53:16
 * @LastEditors: tony10 <EMAIL>
 * @LastEditTime: 2025-08-21 16:28:03
 * @FilePath: /funi-web-mcp/src/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { McpToolContext } from './types'
import { runMain as _runMain, defineCommand } from 'citty'
import { version } from '../package.json'
import { createServer, startServer, stopServer } from './server'
import { registerTechnicalProposalTool } from './tools/technicalProposal'
import { registerUIUXDesignTool } from './tools/funi-UI-UE'
import { registerAutoPMTool } from './tools/funi-pm-auto'

const cli = defineCommand({
  meta: {
    name: 'mcp-instruct',
    version,
    description: 'Run the MCP starter with stdio, http, or sse transport',
  },
  args: {
    http: { type: 'boolean', description: 'Run with HTTP transport' },
    sse: { type: 'boolean', description: 'Run with SSE transport' },
    stdio: { type: 'boolean', description: 'Run with stdio transport (default)' },
    port: { type: 'string', description: 'Port for http/sse (default 3000)', default: '3000' },
    endpoint: { type: 'string', description: 'HTTP endpoint (default /mcp)', default: '/mcp' },
  },
  async run({ args }) {
    const mode = args.http ? 'http' : args.sse ? 'sse' : 'stdio'
    const mcp = createServer({ name: 'FuniA0Mcp', version })

    process.on('SIGTERM', () => stopServer(mcp))
    process.on('SIGINT', () => stopServer(mcp))

    registerTechnicalProposalTool({ mcp } as McpToolContext)
    registerUIUXDesignTool({ mcp } as McpToolContext)
    // 只使用自动化PM工具，避免重复注册
    registerAutoPMTool({ mcp } as McpToolContext)

    if (mode === 'http') {
      await startServer(mcp, { type: 'http', port: Number(args.port), endpoint: args.endpoint })
    }
    else if (mode === 'sse') {
      console.log('Starting SSE server...')
      await startServer(mcp, { type: 'sse', port: Number(args.port) })
    }
    else if (mode === 'stdio') {
      await startServer(mcp, { type: 'stdio' })
    }
  },
})

export const runMain = () => _runMain(cli)
