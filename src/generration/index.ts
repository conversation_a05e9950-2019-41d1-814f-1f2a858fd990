import fs from "fs-extra";
import matter from "gray-matter";
async function main(
  address = "/Users/<USER>/Documents/Work/Funi/Project/Vue/funi-paas/funi-paas-cs-web-cli/src/apps/mcpText/user/readme.md"
) {
  const md = await fs.readFile(address, "utf-8");
  const { data } = matter(md);
  const arr: Record<any, any>[] = [];
  const { directoryStructure } = data;
  for (let i = 0; i < directoryStructure.length; i++) {
    const { dir, temeplate, writeOptions } = directoryStructure[i];
    const res = await fetch(temeplate);
    const text = await res.text();
    fs.ensureFileSync(dir);
    fs.writeFileSync(dir, text);
    if (writeOptions) {
      arr.push({
        dir,
        writeOptions,
      });
    }
  }
  return arr;
}

export default main;
