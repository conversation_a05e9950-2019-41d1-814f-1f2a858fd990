import type { McpToolContext } from '../types'
import { z } from 'zod'
import { ResourceManager } from '../pm/resource-manager'
import { SessionConfigManager } from '../pm/session-config-manager'
import fs from 'fs-extra'

// 会话管理器缓存
const sessionManagers = new Map<string, SessionConfigManager>()

/**
 * 获取或创建会话配置管理器
 */
function getSessionManager(sessionId?: string, userId?: string, projectPath?: string): SessionConfigManager {
  // 如果提供了sessionId，尝试从缓存获取
  if (sessionId && sessionManagers.has(sessionId)) {
    return sessionManagers.get(sessionId)!
  }

  // 创建新的会话管理器
  const manager = new SessionConfigManager(sessionId, userId, projectPath)
  const actualSessionId = manager.getSessionId()
  
  // 缓存管理器
  sessionManagers.set(actualSessionId, manager)
  
  return manager
}

export function registerSessionPMTool({ mcp }: McpToolContext): void {
  const resourceManager = new ResourceManager()

  // 会话化配置管理工具
  mcp.tool(
    'pm-session-config',
    `产品经理会话化配置管理工具 - 支持多用户并发使用

使用方法：
1. 创建/获取会话：{"action": "init", "userId": "user123", "projectPath": "/path/to/project"}
2. 设置 PRD 文件路径：{"action": "set-prd", "path": "/path/to/PRD.md", "sessionId": "session123"}
3. 设置输出目录：{"action": "set-output", "path": "/path/to/output", "sessionId": "session123"}
4. 查看配置状态：{"action": "status", "sessionId": "session123"}
5. 列出活跃会话：{"action": "list-sessions"}
6. 清理过期会话：{"action": "cleanup"}`,
    {
      action: z.enum(['init', 'set-prd', 'set-output', 'status', 'list-sessions', 'cleanup']).describe('操作类型'),
      sessionId: z.string().optional().describe('会话ID'),
      userId: z.string().optional().describe('用户ID'),
      projectPath: z.string().optional().describe('项目路径'),
      path: z.string().optional().describe('文件或目录路径'),
    },
    async ({ action, sessionId, userId, projectPath, path: filePath }) => {
      try {
        switch (action) {
          case 'init': {
            const manager = getSessionManager(sessionId, userId, projectPath)
            await manager.loadConfig()
            const status = manager.getConfigStatus()
            
            return {
              content: [{
                type: 'text',
                text: `会话初始化成功！
会话ID: ${status.sessionId}
用户ID: ${status.sessionInfo.userId || '未指定'}
项目路径: ${status.sessionInfo.projectPath || '未指定'}
配置状态: ${status.isComplete ? '完整' : '不完整'}

${!status.isComplete ? '请继续设置 PRD 文件路径和输出目录。' : ''}`
              }]
            }
          }

          case 'set-prd': {
            if (!sessionId) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供会话ID'
                }]
              }
            }
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供 PRD 文件路径'
                }]
              }
            }

            const manager = getSessionManager(sessionId)
            await manager.loadConfig()
            await manager.setPrdFilePath(filePath)
            
            return {
              content: [{
                type: 'text',
                text: `PRD 文件路径已设置为: ${filePath}
会话ID: ${sessionId}`
              }]
            }
          }

          case 'set-output': {
            if (!sessionId) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供会话ID'
                }]
              }
            }
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供输出目录路径'
                }]
              }
            }

            const manager = getSessionManager(sessionId)
            await manager.loadConfig()
            await manager.setOutputDirectory(filePath)
            
            // 复制资源文件
            await resourceManager.copyAssetsToTarget(filePath)
            
            return {
              content: [{
                type: 'text',
                text: `输出目录已设置为: ${filePath}
资源文件已自动复制到该目录
会话ID: ${sessionId}`
              }]
            }
          }

          case 'status': {
            if (!sessionId) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供会话ID'
                }]
              }
            }

            const manager = getSessionManager(sessionId)
            await manager.loadConfig()
            const status = manager.getConfigStatus()
            
            return {
              content: [{
                type: 'text',
                text: `会话配置状态：
会话ID: ${status.sessionId}
用户ID: ${status.sessionInfo.userId || '未指定'}
项目路径: ${status.sessionInfo.projectPath || '未指定'}
创建时间: ${new Date(status.sessionInfo.createdAt).toLocaleString()}
最后使用: ${new Date(status.sessionInfo.lastUsedAt).toLocaleString()}

配置信息:
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 完整' : '❌ 不完整'}

${!status.isComplete ? '\n请先完成配置设置后再使用页面生成功能。' : ''}`
              }]
            }
          }

          case 'list-sessions': {
            const sessions = await SessionConfigManager.listActiveSessions()
            
            if (sessions.length === 0) {
              return {
                content: [{
                  type: 'text',
                  text: '当前没有活跃的会话'
                }]
              }
            }

            const sessionList = sessions.map(session => 
              `- 会话ID: ${session.sessionId}
  用户ID: ${session.userId || '未指定'}
  项目路径: ${session.projectPath || '未指定'}
  最后使用: ${new Date(session.lastUsedAt).toLocaleString()}`
            ).join('\n\n')

            return {
              content: [{
                type: 'text',
                text: `活跃会话列表 (${sessions.length} 个):\n\n${sessionList}`
              }]
            }
          }

          case 'cleanup': {
            const cleanedCount = await SessionConfigManager.cleanupExpiredSessions(24)
            
            return {
              content: [{
                type: 'text',
                text: `会话清理完成，已清理 ${cleanedCount} 个过期会话（超过24小时未使用）`
              }]
            }
          }

          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `操作失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 会话化页面生成工具
  mcp.tool(
    'pm-session-generate',
    `产品经理会话化页面生成工具 - 根据会话配置生成 HTML 页面

使用方法：
1. 生成入口页面：{"action": "start", "sessionId": "session123"}
2. 生成模块页面：{"action": "page", "module": "用户管理", "sessionId": "session123"}
3. 检查会话配置：{"action": "check", "sessionId": "session123"}`,
    {
      action: z.enum(['start', 'page', 'check']).describe('生成操作类型'),
      sessionId: z.string().describe('会话ID'),
      module: z.string().optional().describe('模块名称（action为page时必需）'),
    },
    async ({ action, sessionId, module }) => {
      try {
        if (!sessionId) {
          return {
            isError: true,
            content: [{
              type: 'text' as const,
              text: '请提供会话ID'
            }]
          }
        }

        const manager = getSessionManager(sessionId)
        await manager.loadConfig()
        
        // 检查配置完整性
        if (!manager.isConfigComplete() && action !== 'check') {
          return {
            isError: true,
            content: [{
              type: 'text' as const,
              text: `会话 ${sessionId} 配置不完整，请先使用 pm-session-config 工具设置 PRD 文件路径和输出目录`
            }]
          }
        }

        switch (action) {
          case 'check': {
            const status = manager.getConfigStatus()
            return {
              content: [{
                type: 'text',
                text: `会话 ${sessionId} 配置检查结果：
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 可以开始生成页面' : '❌ 请完成配置设置'}`
              }]
            }
          }

          case 'start': {
            const startPrompt = await resourceManager.getPromptContent('start.md')
            const baseTemplate = await resourceManager.getTemplateContent('base-template.html')
            
            // 读取 PRD 文件内容
            const prdPath = manager.getPrdFilePath()!
            const prdContent = await fs.readFile(prdPath, 'utf-8')
            
            return {
              content: [{
                type: 'text',
                text: `请使用以下提示词和模板生成入口页面：

=== 会话信息 ===
会话ID: ${sessionId}

=== 提示词 ===
${startPrompt}

=== PRD 内容 ===
${prdContent}

=== 基础模板 ===
${baseTemplate}

=== 生成说明 ===
请根据上述提示词、PRD 内容和基础模板，生成完整的 index.html 文件。
生成的文件应保存到：${manager.getOutputDirectory()}/index.html`
              }]
            }
          }

          case 'page': {
            if (!module) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供模块名称'
                }]
              }
            }
            
            const pagePrompt = await resourceManager.getPromptContent('core/generate-page.md')
            const prdPath = manager.getPrdFilePath()!
            const prdContent = await fs.readFile(prdPath, 'utf-8')
            
            return {
              content: [{
                type: 'text',
                text: `请使用以下提示词生成 ${module} 模块的页面：

=== 会话信息 ===
会话ID: ${sessionId}

=== 生成指令 ===
生成页面-${module}

=== 提示词 ===
${pagePrompt}

=== PRD 内容 ===
${prdContent}

=== 生成说明 ===
请根据上述提示词和 PRD 内容，为 ${module} 模块生成相应的页面文件。
页面文件应保存到：${manager.getOutputDirectory()}/pages/ 目录下`
              }]
            }
          }

          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的生成操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `页面生成失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 提示词和模板获取工具保持不变，因为它们是无状态的
  mcp.tool(
    'pm-prompt',
    `产品经理提示词获取工具 - 获取指定的提示词内容

使用方法：
1. 获取开始提示词：{"type": "start"}
2. 获取页面生成提示词：{"type": "generate-page"}
3. 获取菜单生成提示词：{"type": "generate-menu"}
4. 获取表单生成提示词：{"type": "generate-form"}
5. 列出所有提示词：{"type": "list"}
6. 获取自定义提示词：{"type": "custom", "path": "core/form/generate-select.md"}`,
    {
      type: z.enum(['start', 'generate-page', 'generate-menu', 'generate-form', 'list', 'custom']).describe('提示词类型'),
      path: z.string().optional().describe('自定义提示词路径（type为custom时必需）'),
    },
    async ({ type, path: promptPath }) => {
      try {
        let content = ''
        
        switch (type) {
          case 'start':
            content = await resourceManager.getPromptContent('start.md')
            break
            
          case 'generate-page':
            content = await resourceManager.getPromptContent('core/generate-page.md')
            break
            
          case 'generate-menu':
            content = await resourceManager.getPromptContent('core/generate-menu.md')
            break
            
          case 'generate-form':
            content = await resourceManager.getPromptContent('core/form/generate-form.md')
            break
            
          case 'list':
            const prompts = await resourceManager.listPrompts()
            content = `可用的提示词文件：\n\n${prompts.map(p => `- ${p}`).join('\n')}`
            break
            
          case 'custom':
            if (!promptPath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供提示词文件路径'
                }]
              }
            }
            content = await resourceManager.getPromptContent(promptPath)
            break
        }

        return {
          content: [{
            type: 'text',
            text: content
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `获取提示词失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  mcp.tool(
    'pm-template',
    `产品经理模板获取工具 - 获取指定的模板内容

使用方法：
1. 获取基础模板：{"type": "base"}
2. 获取列表页模板：{"type": "list"}
3. 获取表单页模板：{"type": "form"}
4. 获取详情页模板：{"type": "detail"}
5. 列出所有模板：{"type": "list-all"}
6. 获取自定义模板：{"type": "custom", "path": "templates/custom-template.html"}`,
    {
      type: z.enum(['base', 'list', 'form', 'detail', 'list-all', 'custom']).describe('模板类型'),
      path: z.string().optional().describe('自定义模板路径（type为custom时必需）'),
    },
    async ({ type, path: templatePath }) => {
      try {
        let content = ''
        
        switch (type) {
          case 'base':
            content = await resourceManager.getTemplateContent('base-template.html')
            break
            
          case 'list':
            content = await resourceManager.getTemplateContent('templates/list-page-template.html')
            break
            
          case 'form':
            content = await resourceManager.getTemplateContent('templates/form-page-template.html')
            break
            
          case 'detail':
            content = await resourceManager.getTemplateContent('templates/detail-page-template.html')
            break
            
          case 'list-all':
            const templates = await resourceManager.listTemplates()
            content = `可用的模板文件：\n\n${templates.map(t => `- ${t}`).join('\n')}`
            break
            
          case 'custom':
            if (!templatePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供模板文件路径'
                }]
              }
            }
            content = await resourceManager.getTemplateContent(templatePath)
            break
        }

        return {
          content: [{
            type: 'text',
            text: content
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `获取模板失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )
}
