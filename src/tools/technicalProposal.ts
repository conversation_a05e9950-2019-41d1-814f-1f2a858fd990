/*
 * @Author: 罗前 <EMAIL>
 * @Date: 2025-07-02 16:52:55
 * @LastEditors: 罗前 <EMAIL>
 * @LastEditTime: 2025-07-02 17:13:41
 * @FilePath: /github-funi-mcp/src/tools/technicalProposal.ts
 * @Description: 技术方案设计MCP工具
 */
import type { McpToolContext } from '../types'
import * as dotenv from 'dotenv'
import { z } from 'zod'

dotenv.config()

// 技术方案设计参数接口
interface TechnicalProposalParams {
  productRequirement?: string
  functionalModules?: string
  technicalConstraints?: string
  fileContent?: string
  fileName?: string
}

// 解析用户输入，提取技术方案设计的关键信息
function parseUserInput(input: string): TechnicalProposalParams {
  const params: TechnicalProposalParams = {}

  // 尝试解析结构化输入（JSON格式）
  try {
    const parsed = JSON.parse(input)
    if (parsed.productRequirement)
      params.productRequirement = parsed.productRequirement
    if (parsed.functionalModules)
      params.functionalModules = parsed.functionalModules
    if (parsed.technicalConstraints)
      params.technicalConstraints = parsed.technicalConstraints
    if (parsed.fileContent)
      params.fileContent = parsed.fileContent
    if (parsed.fileName)
      params.fileName = parsed.fileName
    return params
  }
  catch {
    // 如果不是JSON格式，则作为普通文本处理
  }

  // 通过关键词匹配提取信息
  const lines = input.split('\n').map(line => line.trim()).filter(line => line)

  for (const line of lines) {
    const lowerLine = line.toLowerCase()

    // 匹配产品需求
    if (lowerLine.includes('产品需求') || lowerLine.includes('prd')
      || lowerLine.includes('需求文档') || lowerLine.includes('product requirement')) {
      params.productRequirement = line.replace(/^[^：:]*[：:]?\s*/, '')
    }

    // 匹配功能模块
    if (lowerLine.includes('功能模块') || lowerLine.includes('模块清单')
      || lowerLine.includes('functional modules') || lowerLine.includes('功能清单')) {
      params.functionalModules = line.replace(/^[^：:]*[：:]?\s*/, '')
    }

    // 匹配技术约束
    if (lowerLine.includes('技术约束') || lowerLine.includes('约束条件')
      || lowerLine.includes('technical constraints') || lowerLine.includes('技术限制')) {
      params.technicalConstraints = line.replace(/^[^：:]*[：:]?\s*/, '')
    }
  }

  // 如果没有匹配到具体字段，将整个输入作为产品需求
  if (!params.productRequirement && !params.functionalModules && !params.technicalConstraints) {
    params.productRequirement = input
  }

  return params
}

// 检查参数完整性并生成提示信息
function checkParametersAndGeneratePrompt(params: TechnicalProposalParams): {
  isComplete: boolean
  missingParams: string[]
  promptQuestions: string[]
} {
  const missingParams: string[] = []
  const promptQuestions: string[] = []

  if (!params.productRequirement) {
    missingParams.push('产品需求')
    promptQuestions.push('请提供产品需求文档（PRD）内容或上传相关文件')
  }

  if (!params.functionalModules) {
    missingParams.push('功能模块')
    promptQuestions.push('请提供功能模块清单或详细的功能描述')
  }

  if (!params.technicalConstraints) {
    missingParams.push('技术约束')
    promptQuestions.push('请描述技术约束条件（如技术栈、性能要求、兼容性等）')
  }

  return {
    isComplete: missingParams.length === 0,
    missingParams,
    promptQuestions,
  }
}

// 生成完整的技术方案设计提示词
function generateTechnicalProposalPrompt(params: TechnicalProposalParams): string {
  return `# 技术方案设计AI助手

## 角色定义
你是一名资深的前端架构师，具有丰富的Vue3、低代码平台和企业级应用开发经验。

## 任务目标
基于以下输入信息，设计完整的技术方案和系统架构。

## 输入信息
**产品需求：** ${params.productRequirement || '待补充'}
**功能模块：** ${params.functionalModules || '待补充'}
**技术约束：** ${params.technicalConstraints || '待补充'}
${params.fileName ? `**上传文件：** ${params.fileName}` : ''}
${params.fileContent ? `**文件内容：**\n\`\`\`\n${params.fileContent}\n\`\`\`` : ''}

## 技术栈约束
- 前端：Vue3 + Element Plus + Pinia + Vue Router
- 构建工具：Vite
- 代码规范：ESLint + Prettier
- 后端接口：基于低代码平台的RESTful API
- 部署：Docker + Nginx

## 设计原则
1. 简单优于复杂：选择最简单可行的技术方案
2. 一致性优先：确保代码风格和架构模式统一
3. 可维护性：代码易于理解、修改和扩展
4. 性能考虑：避免过度渲染和不必要的网络请求

## 输出要求

### 1. 系统架构设计
- 整体架构图和说明
- 模块依赖关系图
- 数据流向设计
- 状态管理方案（Pinia stores设计）

### 2. 前端工程结构
请提供详细的目录结构：
\`\`\`
src/
├── components/     # 公共组件
│   ├── common/     # 通用组件
│   └── business/   # 业务组件
├── views/         # 页面组件
├── api/           # API调用封装
├── stores/        # Pinia状态管理
├── utils/         # 工具函数
├── composables/   # Vue3组合式函数
├── router/        # 路由配置
├── types/         # TypeScript类型定义
└── assets/        # 静态资源
\`\`\`

### 3. 核心组件设计
为每个主要功能模块设计组件：
- 组件命名规范
- 组件结构模板
- Props和Events定义
- 样式编写规范（使用Element Plus主题）

### 4. API集成方案
- 接口调用封装（基于axios）
- 统一错误处理机制
- 请求/响应拦截器设计
- 数据格式转换和验证
- 接口缓存策略

### 5. 状态管理设计
- Pinia stores结构设计
- 全局状态和局部状态划分
- 状态持久化方案
- 状态更新和同步机制

### 6. 路由设计
- 路由结构和层级
- 路由守卫和权限控制
- 懒加载和代码分割
- 路由元信息配置

### 7. 性能优化策略
- 组件懒加载方案
- 图片和资源优化
- 打包优化配置
- 运行时性能监控

### 8. 开发规范和工具配置
- ESLint和Prettier配置
- Git提交规范
- 组件开发模板
- 单元测试策略

## AI约束和质量要求
- **技术栈一致性**：严格基于Vue3+Element Plus+现有框架进行设计
- **组件复用性**：最大化复用现有组件，避免重复开发
- **架构简洁性**：避免过度抽象，采用最直接的实现方案
- **代码一致性**：确保所有组件遵循统一的代码规范和模式
- **性能实用性**：性能优化方案必须切实可行，避免过度优化

## 质量检查点
1. **技术栈符合性检查**：验证所有技术选型都在现有技术栈范围内
2. **组件复用率评估**：检查现有组件的复用程度，避免重复开发
3. **架构合理性审查**：确保架构设计简洁明了，易于理解和维护
4. **接口一致性验证**：检查API接口设计的一致性和规范性
5. **性能方案可行性评估**：验证性能优化方案的实际效果和实施难度

请基于以上要求，结合输入的产品需求和功能模块，设计出完整的技术方案。`
}

export function registerTechnicalProposalTool({ mcp }: McpToolContext): void {
  mcp.tool(
    'technical-proposal',
    `技术方案设计工具 - 帮助您设计完整的前端技术方案和系统架构。

使用方法：
1. 提供产品需求文档（PRD）内容
2. 描述功能模块清单
3. 说明技术约束条件
4. 支持上传相关文档文件

支持输入格式：
- 自然语言描述各个要素
- JSON格式结构化输入：
  {
    "productRequirement": "产品需求描述",
    "functionalModules": "功能模块清单",
    "technicalConstraints": "技术约束条件",
    "fileContent": "上传文件内容",
    "fileName": "文件名称"
  }

示例输入：
- "产品需求：开发一个企业级项目管理系统；功能模块：项目创建、任务分配、进度跟踪；技术约束：基于Vue3+Element Plus"
- 或直接上传PRD文档，工具会自动解析内容`,
    {
      userInput: z.string().describe('用户输入的技术方案设计信息（可以是描述或文件内容）'),
    },
    async ({ userInput }) => {
      if (!userInput || userInput.trim() === '') {
        return {
          isError: true,
          content: [
            {
              type: 'text' as const,
              text: '请提供技术方案设计信息。您可以描述产品需求、功能模块和技术约束，或者上传相关文档。',
            },
          ],
        }
      }

      try {
        // 解析用户输入
        const params = parseUserInput(userInput.trim())

        // 检查参数完整性
        const { isComplete, missingParams, promptQuestions } = checkParametersAndGeneratePrompt(params)

        if (!isComplete) {
          // 如果信息不完整，引导用户补充
          const missingInfo = missingParams.join('、')
          const questions = promptQuestions.map((q, index) => `${index + 1}. ${q}`).join('\n')

          return {
            content: [
              {
                type: 'text',
                text: `收到您的信息，但还需要补充以下内容才能进行完整的技术方案设计：

缺失信息：${missingInfo}

请补充以下信息：
${questions}

已收集到的信息：
${params.productRequirement ? `- 产品需求：${params.productRequirement}` : ''}
${params.functionalModules ? `- 功能模块：${params.functionalModules}` : ''}
${params.technicalConstraints ? `- 技术约束：${params.technicalConstraints}` : ''}
${params.fileName ? `- 上传文件：${params.fileName}` : ''}

您可以继续提供缺失的信息，我会帮您完善技术方案设计。`,
              },
            ],
          }
        }

        // 信息完整，生成技术方案设计提示词
        const proposalPrompt = generateTechnicalProposalPrompt(params)

        return {
          content: [
            {
              type: 'text',
              text: `已收集完整的技术方案设计信息，正在为您生成技术方案设计提示词：

收集到的信息：
- 产品需求：${params.productRequirement}
- 功能模块：${params.functionalModules}
- 技术约束：${params.technicalConstraints}
${params.fileName ? `- 上传文件：${params.fileName}` : ''}

生成的完整技术方案设计提示词：

${proposalPrompt}`,
            },
          ],
        }
      }
      catch (error) {
        return {
          isError: true,
          content: [
            {
              type: 'text' as const,
              text: `处理技术方案设计信息时出现错误：${error instanceof Error ? error.message : '未知错误'}。请检查输入格式并重试。`,
            },
          ],
        }
      }
    },
  )
}
