import type { McpToolContext } from '../types'
import { z } from 'zod'
import { ResourceManager } from '../pm/resource-manager'
import { AutoSessionManager } from '../pm/auto-session-manager'
import fs from 'fs-extra'

export function registerAutoPMTool({ mcp }: McpToolContext): void {
  const resourceManager = new ResourceManager()

  // 自动配置管理工具
  mcp.tool(
    'pm-auto-config',
    `产品经理自动配置管理工具 - 自动检测用户环境，无需手动指定用户信息

使用方法：
1. 设置 PRD 文件路径：{"action": "set-prd", "path": "/path/to/PRD.md"}
2. 设置输出目录：{"action": "set-output", "path": "/path/to/output"}
3. 查看配置状态：{"action": "status"}
4. 查看会话信息：{"action": "session-info"}
5. 诊断初始化问题：{"action": "diagnose"}

注意：
- 会自动检测用户环境和项目路径，无需手动指定会话ID
- 设置输出目录时会自动复制 assets 和 script 目录
- 如果资源文件未复制，请使用 diagnose 操作排查问题`,
    {
      action: z.enum(['set-prd', 'set-output', 'status', 'session-info', 'diagnose']).describe('操作类型'),
      path: z.string().optional().describe('文件或目录路径（set-prd 和 set-output 时必需）'),
      workingDirectory: z.string().optional().describe('工作目录（可选，默认使用当前目录）'),
    },
    async ({ action, path: filePath, workingDirectory }) => {
      try {
        // 自动获取会话管理器
        const sessionManager = await AutoSessionManager.getSessionManager({
          workingDirectory
        })

        switch (action) {
          case 'set-prd':
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供 PRD 文件路径'
                }]
              }
            }
            await sessionManager.setPrdFilePath(filePath)
            return {
              content: [{
                type: 'text',
                text: `PRD 文件路径已设置为: ${filePath}`
              }]
            }

          case 'set-output':
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供输出目录路径'
                }]
              }
            }

            try {
              await sessionManager.setOutputDirectory(filePath)
              console.log(`输出目录设置成功: ${filePath}`)

              // 自动复制资源文件
              await resourceManager.copyAssetsToTarget(filePath)
              console.log(`资源文件复制完成: ${filePath}`)

              // 验证复制结果
              const fs = await import('fs-extra')
              const path = await import('path')
              const assetsPath = path.join(filePath, 'assets')
              const scriptPath = path.join(filePath, 'script')

              const assetsExists = await fs.pathExists(assetsPath)
              const scriptExists = await fs.pathExists(scriptPath)

              let verificationMsg = ''
              if (assetsExists && scriptExists) {
                verificationMsg = '\n\n✅ 验证成功：\n- assets/ 目录已创建\n- script/ 目录已创建\n- 静态资源文件已复制\n- 启动脚本已复制'
              } else {
                verificationMsg = '\n\n⚠️ 验证警告：\n' +
                  (assetsExists ? '- assets/ 目录 ✅\n' : '- assets/ 目录 ❌\n') +
                  (scriptExists ? '- script/ 目录 ✅' : '- script/ 目录 ❌')
              }

              return {
                content: [{
                  type: 'text',
                  text: `✅ 输出目录已设置为: ${filePath}

📦 资源文件已自动复制到该目录${verificationMsg}

💡 您现在可以：
1. 使用"生成入口页面"命令创建 index.html
2. 使用"生成页面-[模块名]"命令创建具体页面
3. 进入输出目录，使用 script/ 中的启动脚本预览页面`
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `❌ 设置输出目录失败: ${error instanceof Error ? error.message : '未知错误'}

请检查：
1. 目录路径是否正确
2. 是否有写入权限
3. 磁盘空间是否充足`
                }]
              }
            }

          case 'status':
            const status = sessionManager.getConfigStatus()

            // 检查资源文件复制状态
            let resourceStatus = ''
            if (status.hasOutputDir && status.outputDirectory) {
              try {
                const fs = await import('fs-extra')
                const path = await import('path')
                const assetsPath = path.join(status.outputDirectory, 'assets')
                const scriptPath = path.join(status.outputDirectory, 'script')

                const assetsExists = await fs.pathExists(assetsPath)
                const scriptExists = await fs.pathExists(scriptPath)

                resourceStatus = `\n\n📦 资源文件状态:
- Assets 目录: ${assetsExists ? '✅ 已复制' : '❌ 未复制'}
- Script 目录: ${scriptExists ? '✅ 已复制' : '❌ 未复制'}`

                if (!assetsExists || !scriptExists) {
                  resourceStatus += '\n\n💡 如需重新复制资源文件，请重新设置输出目录'
                }
              } catch (error) {
                resourceStatus = '\n\n⚠️ 无法检查资源文件状态'
              }
            }

            return {
              content: [{
                type: 'text',
                text: `📋 配置状态：
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 完整' : '❌ 不完整'}${resourceStatus}

${!status.isComplete ? '\n⚠️ 请先完成配置设置后再使用页面生成功能。' : '\n✅ 配置完整，可以开始生成页面！'}`
              }]
            }

          case 'session-info':
            const sessionInfo = sessionManager.getSessionInfo()
            const projectInfo = AutoSessionManager.detectProjectInfo({ workingDirectory })
            return {
              content: [{
                type: 'text',
                text: `会话信息：
- 会话ID: ${sessionManager.getSessionId()}
- 用户标识: ${projectInfo.userId}
- 项目路径: ${projectInfo.projectPath}
- 项目名称: ${projectInfo.projectName}
- 创建时间: ${new Date(sessionInfo.createdAt).toLocaleString()}
- 最后使用: ${new Date(sessionInfo.lastUsedAt).toLocaleString()}`
              }]
            }

          case 'diagnose':
            try {
              const status = sessionManager.getConfigStatus()
              const fs = await import('fs-extra')
              const path = await import('path')

              let diagnosis = '🔍 项目初始化诊断报告\n\n'

              // 检查配置状态
              diagnosis += '📋 配置检查:\n'
              diagnosis += `- PRD 文件: ${status.hasPrdFile ? '✅ 已设置' : '❌ 未设置'}\n`
              diagnosis += `- 输出目录: ${status.hasOutputDir ? '✅ 已设置' : '❌ 未设置'}\n`

              if (!status.hasOutputDir) {
                diagnosis += '\n❌ 问题发现: 输出目录未设置\n'
                diagnosis += '💡 解决方案: 请提供输出目录路径，我来为您设置\n'
                return {
                  content: [{
                    type: 'text',
                    text: diagnosis
                  }]
                }
              }

              // 检查输出目录是否存在
              const outputDir = status.outputDirectory!
              const outputExists = await fs.pathExists(outputDir)
              diagnosis += `- 输出目录存在: ${outputExists ? '✅ 存在' : '❌ 不存在'}\n`

              if (!outputExists) {
                diagnosis += '\n❌ 问题发现: 输出目录不存在\n'
                diagnosis += '💡 解决方案: 请重新设置输出目录，系统会自动创建\n'
                return {
                  content: [{
                    type: 'text',
                    text: diagnosis
                  }]
                }
              }

              // 检查资源文件
              const assetsPath = path.join(outputDir, 'assets')
              const scriptPath = path.join(outputDir, 'script')
              const assetsExists = await fs.pathExists(assetsPath)
              const scriptExists = await fs.pathExists(scriptPath)

              diagnosis += '\n📦 资源文件检查:\n'
              diagnosis += `- Assets 目录: ${assetsExists ? '✅ 已复制' : '❌ 未复制'}\n`
              diagnosis += `- Script 目录: ${scriptExists ? '✅ 已复制' : '❌ 未复制'}\n`

              if (!assetsExists || !scriptExists) {
                diagnosis += '\n❌ 问题发现: 资源文件未完整复制\n'
                diagnosis += '💡 解决方案: 重新设置输出目录将自动复制资源文件\n'
                diagnosis += '\n🔧 可能的原因:\n'
                diagnosis += '1. 初始化时只设置了PRD文件，未设置输出目录\n'
                diagnosis += '2. 设置输出目录时发生了错误\n'
                diagnosis += '3. 权限问题导致复制失败\n'
              } else {
                // 检查资源文件内容
                const assetsFiles = await fs.readdir(assetsPath)
                const scriptFiles = await fs.readdir(scriptPath)

                diagnosis += `- Assets 文件数量: ${assetsFiles.length}\n`
                diagnosis += `- Script 文件数量: ${scriptFiles.length}\n`

                if (assetsFiles.length === 0 || scriptFiles.length === 0) {
                  diagnosis += '\n⚠️ 警告: 资源目录存在但内容为空\n'
                  diagnosis += '💡 解决方案: 重新设置输出目录以重新复制资源\n'
                } else {
                  diagnosis += '\n✅ 诊断结果: 项目初始化正常\n'
                  diagnosis += '🎉 您可以开始生成页面了！\n'
                }
              }

              return {
                content: [{
                  type: 'text',
                  text: diagnosis
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `❌ 诊断失败: ${error instanceof Error ? error.message : '未知错误'}`
                }]
              }
            }



          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `操作失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 自动页面生成工具
  mcp.tool(
    'pm-auto-generate',
    `产品经理自动页面生成工具 - 自动检测用户环境，根据配置生成 HTML 页面

使用方法：
1. 生成入口页面：{"action": "start"}
2. 生成模块页面：{"action": "page", "module": "用户管理"}
3. 检查配置：{"action": "check"}
4. 验证菜单结构：{"action": "validate-menu", "htmlContent": "菜单HTML内容"}
5. 验证页面结构：{"action": "validate-page", "htmlContent": "页面HTML内容", "pageType": "list|form|detail"}
6. 验证表单结构：{"action": "validate-form", "htmlContent": "表单HTML内容"}
7. 验证CSS兼容性：{"action": "validate-css", "htmlContent": "页面HTML内容", "pageType": "list|form|detail"}

注意：会自动检测用户环境，无需手动指定会话ID`,
    {
      action: z.enum(['start', 'page', 'check', 'validate-menu', 'validate-page', 'validate-form', 'validate-css']).describe('生成操作类型'),
      module: z.string().optional().describe('模块名称（action为page时必需）'),
      workingDirectory: z.string().optional().describe('工作目录（可选，默认使用当前目录）'),
      htmlContent: z.string().optional().describe('HTML内容（验证操作时必需）'),
      pageType: z.enum(['list', 'form', 'detail']).optional().describe('页面类型（validate-page/validate-css时必需）'),
      basePath: z.string().optional().describe('基础路径（validate-page时可选）'),
    },
    async ({ action, module, workingDirectory, htmlContent, pageType, basePath }) => {
      try {
        // 自动获取会话管理器
        const sessionManager = await AutoSessionManager.getSessionManager({
          workingDirectory
        })

        // 检查配置完整性
        if (!sessionManager.isConfigComplete() && action !== 'check') {
          return {
            isError: true,
            content: [{
              type: 'text' as const,
              text: '配置不完整，请先使用 pm-auto-config 工具设置 PRD 文件路径和输出目录'
            }]
          }
        }

        switch (action) {
          case 'check':
            const status = sessionManager.getConfigStatus()
            const projectInfo = AutoSessionManager.detectProjectInfo({ workingDirectory })
            return {
              content: [{
                type: 'text',
                text: `配置检查结果：
- 项目: ${projectInfo.projectName}
- 用户: ${projectInfo.userId}
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 可以开始生成页面' : '❌ 请完成配置设置'}`
              }]
            }

          case 'start':
            // 获取必要的提示词和模板
            const menuPrompt = await resourceManager.getPromptContent('core/generate-menu.md')
            const baseTemplate = await resourceManager.getTemplateContent('base-template.html')

            // 读取 PRD 文件内容
            const prdPath = sessionManager.getPrdFilePath()!
            const prdContent = await fs.readFile(prdPath, 'utf-8')

            return {
              content: [{
                type: 'text',
                text: `🎯 生成入口页面任务

请严格按照以下步骤和规范生成 index.html 文件：

## 第一步：解析PRD菜单结构
从PRD文件的"第五部分: 系统功能菜单结构"中提取菜单数据，转换为JSON格式。

## 第二步：严格按照菜单生成规范
**必须严格遵循以下HTML结构规范**：

${menuPrompt}

## 第三步：使用基础模板
基于以下模板生成完整页面：

<!-- TEMPLATE_START -->
${baseTemplate}
<!-- TEMPLATE_END -->

## 第四步：整合PRD内容
参考PRD文件内容：

<!-- PRD_START -->
${prdContent}
<!-- PRD_END -->

## 第五步：生成要求
1. **严格按照 generate-menu.md 中的HTML结构要求**
2. **使用正确的CSS类名**：funi-menu-list, funi-menu-item, funi-menu-link 等
3. **正确的图标格式**：<iconify-icon icon="mdi:xxx" class="funi-menu-icon"></iconify-icon>
4. **准确的路径配置**：使用hash路由格式
5. **完整的嵌套结构**：有子菜单的使用 funi-menu-group 结构

⚠️ **重要**：不要使用通用模板，必须严格按照 generate-menu.md 中定义的HTML结构规范生成菜单！

生成的文件应保存到：${sessionManager.getOutputDirectory()}/index.html`
              }]
            }

          case 'page':
            if (!module) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供模块名称'
                }]
              }
            }
            
            const pagePrompt = await resourceManager.getPromptContent('core/generate-page.md')
            const prdPath2 = sessionManager.getPrdFilePath()!
            const prdContent2 = await fs.readFile(prdPath2, 'utf-8')
            
            return {
              content: [{
                type: 'text',
                text: `请使用以下提示词生成 ${module} 模块的页面：

=== 生成指令 ===
生成页面-${module}

=== 提示词 ===
${pagePrompt}

=== PRD 内容 ===
${prdContent2}

=== 生成说明 ===
请根据上述提示词和 PRD 内容，为 ${module} 模块生成相应的页面文件。
页面文件应保存到：${sessionManager.getOutputDirectory()}/pages/ 目录下`
              }]
            }

          case 'validate-menu':
            if (!htmlContent) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供要验证的HTML内容'
                }]
              }
            }

            try {
              // 动态导入菜单验证器
              const menuValidatorModule = await import('../pm/menu-validator.js')
              const MenuValidator = menuValidatorModule.MenuValidator
              const validationResult = MenuValidator.validateMenuHTML(htmlContent)

              let resultText = '🔍 菜单结构验证结果\n\n'

              if (validationResult.isValid) {
                resultText += '✅ 验证通过：菜单结构符合规范\n\n'
              } else {
                resultText += '❌ 验证失败：菜单结构不符合规范\n\n'
              }

              if (validationResult.errors.length > 0) {
                resultText += '🚨 错误：\n'
                validationResult.errors.forEach((error: string, index: number) => {
                  resultText += `${index + 1}. ${error}\n`
                })
                resultText += '\n'
              }

              if (validationResult.warnings.length > 0) {
                resultText += '⚠️ 警告：\n'
                validationResult.warnings.forEach((warning: string, index: number) => {
                  resultText += `${index + 1}. ${warning}\n`
                })
                resultText += '\n'
              }

              if (validationResult.suggestions.length > 0) {
                resultText += '💡 建议：\n'
                validationResult.suggestions.forEach((suggestion: string, index: number) => {
                  resultText += `${index + 1}. ${suggestion}\n`
                })
              }

              return {
                content: [{
                  type: 'text',
                  text: resultText
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `菜单验证失败: ${error instanceof Error ? error.message : '未知错误'}`
                }]
              }
            }

          case 'validate-page':
            if (!htmlContent || !pageType) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供HTML内容和页面类型（list/form/detail）'
                }]
              }
            }

            try {
              const { PageValidator } = await import('../pm/page-validator.js')
              const validationResult = await PageValidator.validatePageStructure(htmlContent, pageType)

              let resultText = '🔍 页面结构验证结果\n\n'

              if (validationResult.isValid) {
                resultText += `✅ 验证通过：${pageType}页面结构符合规范\n`
                resultText += `📊 合规性评分: ${validationResult.score}/100\n\n`
              } else {
                resultText += `❌ 验证失败：${pageType}页面结构不符合规范\n`
                resultText += `📊 合规性评分: ${validationResult.score}/100\n\n`
              }

              if (validationResult.errors.length > 0) {
                resultText += '🚨 错误：\n'
                validationResult.errors.forEach((error: any, index: number) => {
                  resultText += `${index + 1}. [${error.type}] ${error.message}\n`
                  if (error.suggestion) {
                    resultText += `   💡 建议: ${error.suggestion}\n`
                  }
                })
                resultText += '\n'
              }

              if (validationResult.warnings.length > 0) {
                resultText += '⚠️ 警告：\n'
                validationResult.warnings.forEach((warning: any, index: number) => {
                  resultText += `${index + 1}. [${warning.type}] ${warning.message}\n`
                  if (warning.suggestion) {
                    resultText += `   💡 建议: ${warning.suggestion}\n`
                  }
                })
                resultText += '\n'
              }

              if (validationResult.suggestions.length > 0) {
                resultText += '💡 优化建议：\n'
                validationResult.suggestions.forEach((suggestion: string, index: number) => {
                  resultText += `${index + 1}. ${suggestion}\n`
                })
              }

              // 如果提供了basePath，还要验证路径一致性
              if (basePath) {
                const pathResult = PageValidator.validatePathConsistency(basePath, `pages/${basePath}/${pageType === 'form' ? 'add-edit' : pageType === 'detail' ? 'detail-review' : 'list'}.html`, pageType)
                if (!pathResult.isValid) {
                  resultText += '\n🛣️ 路径一致性检查：\n'
                  pathResult.errors.forEach((error: any, index: number) => {
                    resultText += `${index + 1}. ${error.message}\n`
                    if (error.suggestion) {
                      resultText += `   💡 建议: ${error.suggestion}\n`
                    }
                  })
                }
              }

              return {
                content: [{
                  type: 'text',
                  text: resultText
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `页面验证失败: ${error instanceof Error ? error.message : '未知错误'}`
                }]
              }
            }

          case 'validate-form':
            if (!htmlContent) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供要验证的HTML内容'
                }]
              }
            }

            try {
              const { FormValidator } = await import('../pm/page-validator.js')
              const validationResult = FormValidator.validateFormStructure(htmlContent)

              let resultText = '🔍 表单结构验证结果\n\n'

              if (validationResult.isValid) {
                resultText += '✅ 验证通过：表单结构符合规范\n'
                resultText += `📊 合规性评分: ${validationResult.score}/100\n\n`
              } else {
                resultText += '❌ 验证失败：表单结构不符合规范\n'
                resultText += `📊 合规性评分: ${validationResult.score}/100\n\n`
              }

              if (validationResult.errors.length > 0) {
                resultText += '🚨 错误：\n'
                validationResult.errors.forEach((error: any, index: number) => {
                  resultText += `${index + 1}. [${error.type}] ${error.message}\n`
                  if (error.suggestion) {
                    resultText += `   💡 建议: ${error.suggestion}\n`
                  }
                })
                resultText += '\n'
              }

              if (validationResult.warnings.length > 0) {
                resultText += '⚠️ 警告：\n'
                validationResult.warnings.forEach((warning: any, index: number) => {
                  resultText += `${index + 1}. [${warning.type}] ${warning.message}\n`
                  if (warning.suggestion) {
                    resultText += `   💡 建议: ${warning.suggestion}\n`
                  }
                })
                resultText += '\n'
              }

              if (validationResult.suggestions.length > 0) {
                resultText += '💡 优化建议：\n'
                validationResult.suggestions.forEach((suggestion: string, index: number) => {
                  resultText += `${index + 1}. ${suggestion}\n`
                })
              }

              return {
                content: [{
                  type: 'text',
                  text: resultText
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `表单验证失败: ${error instanceof Error ? error.message : '未知错误'}`
                }]
              }
            }

          case 'validate-css':
            if (!htmlContent || !pageType) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供HTML内容和页面类型（list/form/detail）'
                }]
              }
            }

            try {
              const { CSSFrameworkValidator } = await import('../pm/css-framework-validator.js')
              const validationResult = await CSSFrameworkValidator.validateCSSCompatibility(htmlContent, pageType)

              let resultText = '🎨 CSS框架兼容性验证结果\n\n'

              if (validationResult.isValid) {
                resultText += `✅ 验证通过：CSS框架兼容性良好\n`
                resultText += `📊 兼容性评分: ${validationResult.score}/100\n\n`
              } else {
                resultText += `❌ 验证失败：存在CSS框架兼容性问题\n`
                resultText += `📊 兼容性评分: ${validationResult.score}/100\n\n`
              }

              if (validationResult.errors.length > 0) {
                resultText += '🚨 CSS兼容性错误：\n'
                validationResult.errors.forEach((error: any, index: number) => {
                  resultText += `${index + 1}. [${error.type}] ${error.message}\n`
                  if (error.suggestion) {
                    resultText += `   💡 修复建议: ${error.suggestion}\n`
                  }
                  if (error.correctClass) {
                    resultText += `   🔧 正确类名: ${error.correctClass}\n`
                  }
                })
                resultText += '\n'
              }

              if (validationResult.missingClasses.length > 0) {
                resultText += '🔍 缺失的CSS类名：\n'
                validationResult.missingClasses.forEach((className: string, index: number) => {
                  resultText += `${index + 1}. ${className}\n`
                })
                resultText += '\n'
              }

              if (validationResult.incorrectStructure.length > 0) {
                resultText += '🏗️ DOM结构问题：\n'
                validationResult.incorrectStructure.forEach((structure: any, index: number) => {
                  resultText += `${index + 1}. ${structure.expected}\n`
                  resultText += `   📍 位置: ${structure.location}\n`
                  resultText += `   💡 建议: ${structure.suggestion}\n`
                })
                resultText += '\n'
              }

              if (validationResult.warnings.length > 0) {
                resultText += '⚠️ 警告：\n'
                validationResult.warnings.forEach((warning: any, index: number) => {
                  resultText += `${index + 1}. [${warning.type}] ${warning.message}\n`
                  if (warning.suggestion) {
                    resultText += `   💡 建议: ${warning.suggestion}\n`
                  }
                })
                resultText += '\n'
              }

              // 提供正确的结构模板
              if (validationResult.score < 80) {
                resultText += '📋 正确的DOM结构模板：\n'
                const correctStructure = CSSFrameworkValidator.getCorrectDOMStructure(pageType)
                resultText += '```html\n' + correctStructure.trim() + '\n```\n\n'

                resultText += '🔧 CSS类名映射表：\n'
                const classMapping = CSSFrameworkValidator.getCorrectClassMapping()
                Object.entries(classMapping).forEach(([wrong, correct]) => {
                  resultText += `  ${wrong} → ${correct}\n`
                })
              }

              return {
                content: [{
                  type: 'text',
                  text: resultText
                }]
              }
            } catch (error) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: `CSS框架验证失败: ${error instanceof Error ? error.message : '未知错误'}`
                }]
              }
            }

          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的生成操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `页面生成失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 保留原有的提示词和模板工具（无状态，可以安全并发使用）
  mcp.tool(
    'pm-prompt',
    `产品经理提示词获取工具 - 内部使用，获取页面生成所需的提示词内容

⚠️ 注意：此工具仅供AI智能体内部使用，不建议用户直接调用
如需了解页面生成流程，请使用 pm-auto-generate 工具

使用方法：
1. 获取开始提示词：{"type": "start"}
2. 获取页面生成提示词：{"type": "generate-page"}
3. 获取菜单生成提示词：{"type": "generate-menu"}
4. 获取表单生成提示词：{"type": "generate-form"}
5. 列出所有提示词：{"type": "list"}
6. 获取自定义提示词：{"type": "custom", "path": "core/form/generate-select.md"}`,
    {
      type: z.enum(['start', 'generate-page', 'generate-menu', 'generate-form', 'list', 'custom']).describe('提示词类型'),
      path: z.string().optional().describe('自定义提示词路径（type为custom时必需）'),
      userVisible: z.boolean().optional().describe('是否对用户可见（默认false）'),
    },
    async ({ type, path: promptPath, userVisible = false }) => {
      let promptName = ''
      try {
        let content = ''

        switch (type) {
          case 'start':
            content = await resourceManager.getPromptContent('start.md')
            promptName = '入口页面生成提示词'
            break

          case 'generate-page':
            content = await resourceManager.getPromptContent('core/generate-page.md')
            promptName = '页面生成提示词'
            break

          case 'generate-menu':
            content = await resourceManager.getPromptContent('core/generate-menu.md')
            promptName = '菜单生成提示词'
            break

          case 'generate-form':
            content = await resourceManager.getPromptContent('core/form/generate-form.md')
            promptName = '表单生成提示词'
            break

          case 'list':
            const prompts = await resourceManager.listPrompts()
            content = `可用的提示词文件：\n\n${prompts.map(p => `- ${p}`).join('\n')}`
            promptName = '提示词列表'
            break

          case 'custom':
            if (!promptPath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: userVisible ? '❌ 请提供提示词文件路径' : '请提供提示词文件路径'
                }]
              }
            }
            content = await resourceManager.getPromptContent(promptPath)
            promptName = `自定义提示词 (${promptPath})`
            break
        }

        // 如果用户可见，返回简化信息
        if (userVisible) {
          return {
            content: [{
              type: 'text',
              text: `✅ ${promptName}获取成功\n\n💡 提示：提示词内容已提供给AI智能体用于页面生成，无需查看技术细节。\n如需生成页面，请使用"生成入口页面"或"生成页面-[模块名]"命令。`
            }]
          }
        }

        // AI智能体内部使用，返回完整内容
        return {
          content: [{
            type: 'text',
            text: `<!-- INTERNAL_USE_ONLY -->\n${content}`
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: userVisible ?
              `❌ 获取${promptName || '提示词'}失败：${error instanceof Error ? error.message : '未知错误'}` :
              `获取提示词失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  mcp.tool(
    'pm-template',
    `产品经理模板获取工具 - 内部使用，获取页面生成所需的模板内容

⚠️ 注意：此工具仅供AI智能体内部使用，不建议用户直接调用
如需了解页面结构，请使用 pm-auto-generate 工具生成实际页面

使用方法：
1. 获取基础模板：{"type": "base"}
2. 获取列表页模板：{"type": "list"}
3. 获取表单页模板：{"type": "form"}
4. 获取详情页模板：{"type": "detail"}
5. 列出所有模板：{"type": "list-all"}
6. 获取自定义模板：{"type": "custom", "path": "templates/custom-template.html"}`,
    {
      type: z.enum(['base', 'list', 'form', 'detail', 'list-all', 'custom']).describe('模板类型'),
      path: z.string().optional().describe('自定义模板路径（type为custom时必需）'),
      userVisible: z.boolean().optional().describe('是否对用户可见（默认false）'),
    },
    async ({ type, path: templatePath, userVisible = false }) => {
      let templateName = ''
      try {
        let content = ''

        switch (type) {
          case 'base':
            content = await resourceManager.getTemplateContent('base-template.html')
            templateName = '基础页面模板'
            break

          case 'list':
            content = await resourceManager.getTemplateContent('templates/list-page-template.html')
            templateName = '列表页面模板'
            break

          case 'form':
            content = await resourceManager.getTemplateContent('templates/form-page-template.html')
            templateName = '表单页面模板'
            break

          case 'detail':
            content = await resourceManager.getTemplateContent('templates/detail-page-template.html')
            templateName = '详情页面模板'
            break

          case 'list-all':
            const templates = await resourceManager.listTemplates()
            content = `可用的模板文件：\n\n${templates.map(t => `- ${t}`).join('\n')}`
            templateName = '模板列表'
            break

          case 'custom':
            if (!templatePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: userVisible ? '❌ 请提供模板文件路径' : '请提供模板文件路径'
                }]
              }
            }
            content = await resourceManager.getTemplateContent(templatePath)
            templateName = `自定义模板 (${templatePath})`
            break
        }

        // 如果用户可见，返回简化信息
        if (userVisible) {
          return {
            content: [{
              type: 'text',
              text: `✅ ${templateName}获取成功\n\n💡 提示：模板内容已提供给AI智能体用于页面生成，无需查看技术细节。\n如需生成页面，请使用"生成入口页面"或"生成页面-[模块名]"命令。`
            }]
          }
        }

        // AI智能体内部使用，返回完整内容
        return {
          content: [{
            type: 'text',
            text: `<!-- INTERNAL_USE_ONLY -->\n${content}`
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: userVisible ?
              `❌ 获取${templateName || '模板'}失败：${error instanceof Error ? error.message : '未知错误'}` :
              `获取模板失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )
}
