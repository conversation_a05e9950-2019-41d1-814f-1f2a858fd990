import type { McpToolContext } from '../types'
import { z } from 'zod'
import { ResourceManager } from '../pm/resource-manager'
import { ConfigManager } from '../pm/config-manager'
import { SessionConfigManager } from '../pm/session-config-manager'
import fs from 'fs-extra'
import * as path from 'path'

export function registerPMTool({ mcp }: McpToolContext): void {
  const resourceManager = new ResourceManager()
  const configManager = new ConfigManager()

  // 初始化配置管理器
  configManager.loadConfig().catch(console.error)

  // 配置管理工具
  mcp.tool(
    'pm-config',
    `产品经理配置管理工具 - 管理 PRD 文件路径和页面生成目录配置

使用方法：
1. 设置 PRD 文件路径：{"action": "set-prd", "path": "/path/to/PRD.md"}
2. 设置输出目录：{"action": "set-output", "path": "/path/to/output"}
3. 查看配置状态：{"action": "status"}
4. 重置配置：{"action": "reset"}`,
    {
      action: z.enum(['set-prd', 'set-output', 'status', 'reset']).describe('操作类型'),
      path: z.string().optional().describe('文件或目录路径（set-prd 和 set-output 时必需）'),
    },
    async ({ action, path: filePath }) => {
      try {
        await configManager.loadConfig()

        switch (action) {
          case 'set-prd':
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供 PRD 文件路径'
                }]
              }
            }
            await configManager.setPrdFilePath(filePath)
            return {
              content: [{
                type: 'text',
                text: `PRD 文件路径已设置为: ${filePath}`
              }]
            }

          case 'set-output':
            if (!filePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供输出目录路径'
                }]
              }
            }
            await configManager.setOutputDirectory(filePath)
            // 首次设置输出目录时，自动复制资源文件
            await resourceManager.copyAssetsToTarget(filePath)
            return {
              content: [{
                type: 'text',
                text: `输出目录已设置为: ${filePath}\n资源文件已自动复制到该目录`
              }]
            }

          case 'status':
            const status = configManager.getConfigStatus()
            return {
              content: [{
                type: 'text',
                text: `配置状态：
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 完整' : '❌ 不完整'}

${!status.isComplete ? '\n请先完成配置设置后再使用页面生成功能。' : ''}`
              }]
            }

          case 'reset':
            await configManager.resetConfig()
            return {
              content: [{
                type: 'text',
                text: '配置已重置'
              }]
            }

          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `操作失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 提示词获取工具
  mcp.tool(
    'pm-prompt',
    `产品经理提示词获取工具 - 获取指定的提示词内容

使用方法：
1. 获取开始提示词：{"type": "start"}
2. 获取页面生成提示词：{"type": "generate-page"}
3. 获取菜单生成提示词：{"type": "generate-menu"}
4. 获取表单生成提示词：{"type": "generate-form"}
5. 列出所有提示词：{"type": "list"}
6. 获取自定义提示词：{"type": "custom", "path": "core/form/generate-select.md"}`,
    {
      type: z.enum(['start', 'generate-page', 'generate-menu', 'generate-form', 'list', 'custom']).describe('提示词类型'),
      path: z.string().optional().describe('自定义提示词路径（type为custom时必需）'),
    },
    async ({ type, path: promptPath }) => {
      try {
        let content = ''
        
        switch (type) {
          case 'start':
            content = await resourceManager.getPromptContent('start.md')
            await configManager.addRecentPrompt('start.md')
            break
            
          case 'generate-page':
            content = await resourceManager.getPromptContent('core/generate-page.md')
            await configManager.addRecentPrompt('core/generate-page.md')
            break
            
          case 'generate-menu':
            content = await resourceManager.getPromptContent('core/generate-menu.md')
            await configManager.addRecentPrompt('core/generate-menu.md')
            break
            
          case 'generate-form':
            content = await resourceManager.getPromptContent('core/form/generate-form.md')
            await configManager.addRecentPrompt('core/form/generate-form.md')
            break
            
          case 'list':
            const prompts = await resourceManager.listPrompts()
            const recentPrompts = configManager.getRecentPrompts()
            content = `可用的提示词文件：

最近使用：
${recentPrompts.length > 0 ? recentPrompts.map(p => `- ${p}`).join('\n') : '无'}

所有提示词：
${prompts.map(p => `- ${p}`).join('\n')}`
            break
            
          case 'custom':
            if (!promptPath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供提示词文件路径'
                }]
              }
            }
            content = await resourceManager.getPromptContent(promptPath)
            await configManager.addRecentPrompt(promptPath)
            break
        }

        return {
          content: [{
            type: 'text',
            text: content
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `获取提示词失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 模板获取工具
  mcp.tool(
    'pm-template',
    `产品经理模板获取工具 - 获取指定的模板内容

使用方法：
1. 获取基础模板：{"type": "base"}
2. 获取列表页模板：{"type": "list"}
3. 获取表单页模板：{"type": "form"}
4. 获取详情页模板：{"type": "detail"}
5. 列出所有模板：{"type": "list-all"}
6. 获取自定义模板：{"type": "custom", "path": "templates/custom-template.html"}`,
    {
      type: z.enum(['base', 'list', 'form', 'detail', 'list-all', 'custom']).describe('模板类型'),
      path: z.string().optional().describe('自定义模板路径（type为custom时必需）'),
    },
    async ({ type, path: templatePath }) => {
      try {
        let content = ''

        switch (type) {
          case 'base':
            content = await resourceManager.getTemplateContent('base-template.html')
            break

          case 'list':
            content = await resourceManager.getTemplateContent('templates/list-page-template.html')
            break

          case 'form':
            content = await resourceManager.getTemplateContent('templates/form-page-template.html')
            break

          case 'detail':
            content = await resourceManager.getTemplateContent('templates/detail-page-template.html')
            break

          case 'list-all':
            const templates = await resourceManager.listTemplates()
            content = `可用的模板文件：

${templates.map(t => `- ${t}`).join('\n')}`
            break

          case 'custom':
            if (!templatePath) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供模板文件路径'
                }]
              }
            }
            content = await resourceManager.getTemplateContent(templatePath)
            break
        }

        return {
          content: [{
            type: 'text',
            text: content
          }]
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `获取模板失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )

  // 页面生成工具
  mcp.tool(
    'pm-generate',
    `产品经理页面生成工具 - 根据 PRD 和提示词生成 HTML 页面

使用方法：
1. 生成入口页面：{"action": "start"}
2. 生成模块页面：{"action": "page", "module": "用户管理"}
3. 检查配置：{"action": "check"}

注意：使用前请确保已通过 pm-config 工具配置好 PRD 文件路径和输出目录。`,
    {
      action: z.enum(['start', 'page', 'check']).describe('生成操作类型'),
      module: z.string().optional().describe('模块名称（action为page时必需）'),
    },
    async ({ action, module }) => {
      try {
        await configManager.loadConfig()

        // 检查配置完整性
        if (!configManager.isConfigComplete() && action !== 'check') {
          return {
            isError: true,
            content: [{
              type: 'text' as const,
              text: '配置不完整，请先使用 pm-config 工具设置 PRD 文件路径和输出目录'
            }]
          }
        }

        switch (action) {
          case 'check':
            const status = configManager.getConfigStatus()
            return {
              content: [{
                type: 'text',
                text: `配置检查结果：
- PRD 文件: ${status.hasPrdFile ? `✅ ${status.prdFilePath}` : '❌ 未设置'}
- 输出目录: ${status.hasOutputDir ? `✅ ${status.outputDirectory}` : '❌ 未设置'}
- 配置完整性: ${status.isComplete ? '✅ 可以开始生成页面' : '❌ 请完成配置设置'}`
              }]
            }

          case 'start':
            const startPrompt = await resourceManager.getPromptContent('start.md')
            const baseTemplate = await resourceManager.getTemplateContent('base-template.html')

            // 读取 PRD 文件内容
            const prdPath = configManager.getPrdFilePath()!
            const prdContent = await fs.readFile(prdPath, 'utf-8')

            return {
              content: [{
                type: 'text',
                text: `请使用以下提示词和模板生成入口页面：

=== 提示词 ===
${startPrompt}

=== PRD 内容 ===
${prdContent}

=== 基础模板 ===
${baseTemplate}

=== 生成说明 ===
请根据上述提示词、PRD 内容和基础模板，生成完整的 index.html 文件。
生成的文件应保存到：${configManager.getOutputDirectory()}/index.html`
              }]
            }

          case 'page':
            if (!module) {
              return {
                isError: true,
                content: [{
                  type: 'text' as const,
                  text: '请提供模块名称'
                }]
              }
            }

            const pagePrompt = await resourceManager.getPromptContent('core/generate-page.md')
            const prdPath2 = configManager.getPrdFilePath()!
            const prdContent2 = await fs.readFile(prdPath2, 'utf-8')

            return {
              content: [{
                type: 'text',
                text: `请使用以下提示词生成 ${module} 模块的页面：

=== 生成指令 ===
生成页面-${module}

=== 提示词 ===
${pagePrompt}

=== PRD 内容 ===
${prdContent2}

=== 生成说明 ===
请根据上述提示词和 PRD 内容，为 ${module} 模块生成相应的页面文件。
页面文件应保存到：${configManager.getOutputDirectory()}/pages/ 目录下`
              }]
            }

          default:
            return {
              isError: true,
              content: [{
                type: 'text' as const,
                text: '不支持的生成操作类型'
              }]
            }
        }
      } catch (error) {
        return {
          isError: true,
          content: [{
            type: 'text' as const,
            text: `页面生成失败: ${error instanceof Error ? error.message : '未知错误'}`
          }]
        }
      }
    }
  )
}
