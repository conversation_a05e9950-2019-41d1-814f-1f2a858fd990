/*
 * @Author: 罗前 <EMAIL>
 * @Date: 2025-07-02 15:26:04
 * @LastEditors: 罗前 <EMAIL>
 * @LastEditTime: 2025-07-03 10:00:47
 * @FilePath: /github-funi-mcp/src/tools/funi-UI-UE.ts
 * @Description: UI/UX设计实现MCP工具
 */
import type { McpToolContext } from '../types'
import * as dotenv from 'dotenv'
import { z } from 'zod'

dotenv.config()

// 生成完整的UI/UX设计提示词
function generateUIUXDesignPrompt(): string {
  try {
    // 读取 UIUX_Designer_Agent_Prompt.md 文件
    const promptPath = path.join(process.cwd(), 'UIUX_Designer_Agent_Prompt.md')
    if (fs.existsSync(promptPath)) {
      return fs.readFileSync(promptPath, 'utf-8')
    }
    else {
      return '未找到 UIUX_Designer_Agent_Prompt.md 文件，请确保文件存在于项目根目录。'
    }
  }
  catch (error) {
    console.error('读取 UIUX_Designer_Agent_Prompt.md 文件失败:', error)
    return '读取提示词文件时出现错误，请检查文件是否存在且可读。'
  }
}

export function registerUIUXDesignTool({ mcp }: McpToolContext): void {
  mcp.tool(
    'Funi-UI-UE',
    `UI/UX设计实现工具 - 读取本地docs/PRD.md和docs/User_Story_Map.md文件，生成统一的UI/UX设计提示词。

使用方法：
- 工具会自动读取项目根目录下的docs/PRD.md和docs/User_Story_Map.md文件
- 结合UIUX_Designer_Agent_Prompt.md模板生成完整的设计提示词
- 直接返回提示词供团队成员使用

注意：请确保以下文件存在：
- docs/PRD.md (产品需求文档)
- docs/User_Story_Map.md (用户故事地图)
- UIUX_Designer_Agent_Prompt.md (UI/UX设计提示词模板)`,
    {
      trigger: z.string().optional().describe('触发工具执行'),
    },
    async () => {
      try {
        // 读取本地文档
        const { prdContent, userStoryMap } = readLocalDocs()

        // 生成UI/UX设计提示词
        const designPrompt = generateUIUXDesignPrompt()

        // 组合最终输出
        let finalPrompt = designPrompt

        if (prdContent) {
          finalPrompt += `\n\n## 产品需求文档 (PRD)\n${prdContent}`
        }

        if (userStoryMap) {
          finalPrompt += `\n\n## 用户故事地图\n${userStoryMap}`
        }

        return {
          content: [
            {
              type: 'text',
              text: finalPrompt,
            },
          ],
        }
      }
      catch (error) {
        return {
          isError: true,
          content: [
            {
              type: 'text' as const,
              text: `生成UI/UX设计提示词时出现错误：${error instanceof Error ? error.message : '未知错误'}。请检查相关文件是否存在。`,
            },
          ],
        }
      }
    },
  )
}
