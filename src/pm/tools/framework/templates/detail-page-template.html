<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情/审核</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
</head>

<body>

    <div id="app" class="form-container">
        <div class="detail-header">
            <div class="header-left">
                <div class="header-title">采购计划管理 - 详情</div>
            </div>
            <div class="header-right">
                <div class="approval-info">
                    <span>审批编号: <span id="displayApprovalNumber">CG—20240808—0001</span></span>
                    <span>审批状态: <span id="displayApprovalStatus">待审核</span></span>
                </div>
            </div>
        </div>

        <div class="funi-tabs">
            <div class="tab-item active" data-tab="basic-info">基本信息</div>
            <div class="tab-item" data-tab="operation-log">流程记录</div>
        </div>

        <div class="container-content">
            <div class="form-step-content form-step-1 active" data-tab-content="basic-info">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <div id="planNumber" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label">计划项目名称:</label>
                            <div class="form-item-value">
                                <div id="planProjectName" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="auditStatus" class="form-item-label">审核状态:</label>
                            <div class="form-item-value">
                                <div id="auditStatus" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label">采购类型:</label>
                            <div class="form-item-value">
                                <div id="procurementType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label">采购方式:</label>
                            <div class="form-item-value">
                                <div id="procurementMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label">招标类别:</label>
                            <div class="form-item-value">
                                <div id="biddingCategory" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <div id="budgetAmount" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label">资金来源:</label>
                            <div class="form-item-value">
                                <div id="fundSource" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <div id="biddingTime" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label">采购组织方式:</label>
                            <div class="form-item-value">
                                <div id="procurementOrganizationMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="agency" class="form-item-label">代理机构:</label>
                            <div class="form-item-value">
                                <div id="agency" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <div id="annualProcurementPlan" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label">项目经办人:</label>
                            <div class="form-item-value">
                                <div id="projectHandler" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label">立项决策日期:</label>
                            <div class="form-item-value">
                                <div id="decisionDate" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="rejectionReason" class="form-item-label">驳回原因:</label>
                            <div class="form-item-value">
                                <div id="rejectionReason" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label">项目类型:</label>
                            <div class="form-item-value">
                                <div id="projectType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label">项目业主:</label>
                            <div class="form-item-value">
                                <div id="projectOwner" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <div id="projectBasicInfo" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="secondaryCompany" class="form-item-label">所属二级公司单位:</label>
                            <div class="form-item-value">
                                <div id="secondaryCompany" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <div id="remarks" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="decisionFiles" class="form-item-label">立项决策文件:</label>
                            <div class="form-item-value">
                                <div id="decisionFiles" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="form-step-content" data-tab-content="operation-log">
                <div class="form-section-title">流程记录</div>
                <div class="timeline-container">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">提交审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 张三</span>
                                <span>操作时间: 2024-08-01 10:00:00</span>
                            </div>
                            <div class="timeline-description">计划已提交至审批流程。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">部门初审</div>
                            <div class="timeline-meta">
                                <span>操作人: 李四</span>
                                <span>操作时间: 2024-08-02 11:30:00</span>
                            </div>
                            <div class="timeline-description">初审通过，转交至财务部。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">财务部审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 王五</span>
                                <span>操作时间: 2024-08-03 14:00:00</span>
                            </div>
                            <div class="timeline-description">财务审核通过，预算充足。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">最终审批</div>
                            <div class="timeline-meta">
                                <span>操作人: 赵六</span>
                                <span>操作时间: 2024-08-04 09:00:00</span>
                            </div>
                            <div class="timeline-description">审批通过，计划正式生效。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <script>
        // ===== iframe环境兼容的状态管理 =====
        // 使用window对象避免变量重复声明
        window.pageState = window.pageState || {
            isInitialized: false,
            currentTab: 'basic-info',
            currentId: null
        };

        // 页面状态重置函数
        function resetPageState() {
            window.pageState.isInitialized = false;
            window.pageState.currentTab = 'basic-info';
            window.pageState.currentId = null;
        }

        // Tab switching initialization with iframe compatibility
        function initTabSwitching() {
            const tabItems = document.querySelectorAll('.funi-tabs .tab-item');
            const tabContents = document.querySelectorAll('.form-step-content');

            // Check if elements are found, retry if not
            if (tabItems.length === 0 || tabContents.length === 0) {
                setTimeout(initTabSwitching, 100);
                return;
            }

            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    // Remove active class from all tab items and contents
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to the clicked tab item
                    item.classList.add('active');

                    // Show the corresponding tab content
                    const targetTab = item.dataset.tab;
                    const targetContent = document.querySelector(`[data-tab-content="${targetTab}"]`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }

        // Page data initialization with iframe compatibility
        function initPageData() {
            // 每次初始化时重置状态，避免缓存问题
            resetPageState();
            window.pageState.isInitialized = true;

            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            const reviewParam = urlParams.get('review');
            window.pageState.currentId = id;

            if (id) {
                // Load data logic here
                loadPageData(id);
            }

            if (reviewParam === '1') {
                // 检查是否已存在form-actions，避免重复创建
                const existingFormActions = document.querySelector('.form-actions');
                if (!existingFormActions) {
                    const appDiv = document.getElementById('app');
                    const formActionsDiv = document.createElement('div');
                    formActionsDiv.className = 'form-actions';
                    formActionsDiv.innerHTML = `
                    <button class="button primary" id="approveButton">通过</button>
                    <button class="button" id="rejectButton">驳回</button>
                `;
                    appDiv.appendChild(formActionsDiv);

                    document.getElementById('approveButton').addEventListener('click', () => {
                        alert('审批通过！');
                        parent.window.location.hash = '#/procurement-plan-management'; // Go back to list page
                    });

                    document.getElementById('rejectButton').addEventListener('click', () => {
                        const reason = prompt('请输入驳回原因:');
                        if (reason) {
                            alert(`审批驳回，原因: ${reason}`);
                            parent.window.location.hash = '#/procurement-plan-management'; // Go back to list page
                        } else {
                            alert('驳回操作已取消。');
                        }
                    });
                }
            }
        }

        function loadPageData(id) {
            // In a real application, fetch data by ID and populate the form
            // For demo, we'll use dummy data
            const dummyData = {
                    planNumber: id,
                    planProjectName: '某公司2024年度办公用品采购计划',
                    auditStatus: '待审核',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    biddingCategory: '货物类(材料/设备/供应及安装)',
                    budgetAmount: 150.00,
                    fundSource: '自有资金',
                    biddingTime: '2024年第三季度',
                    procurementOrganizationMethod: '自主招标',
                    agency: ['无'],
                    annualProcurementPlan: 140.00,
                    projectHandler: '张三',
                    decisionDate: '2024-08-01',
                    rejectionReason: '无',
                    projectType: '依法必须招标项目',
                    projectOwner: '行政部',
                    projectBasicInfo: '本计划旨在采购公司日常运营所需的各类办公用品，以确保各部门工作的顺利进行。采购范围包括但不限于文具、纸张、打印耗材、小型办公设备等。',
                    secondaryCompany: '集团本部',
                    remarks: '请优先考虑环保型产品。',
                    decisionFiles: '立项决策文件.pdf, 附件说明.docx'
                };

                // Update header info with element existence check
                const displayApprovalNumberEl = document.getElementById('displayApprovalNumber');
                if (displayApprovalNumberEl) displayApprovalNumberEl.textContent = dummyData.planNumber;
                
                const displayApprovalStatusEl = document.getElementById('displayApprovalStatus');
                if (displayApprovalStatusEl) displayApprovalStatusEl.textContent = dummyData.auditStatus;
                
                const headerTitleEl = document.querySelector('.header-title');
                if (headerTitleEl) headerTitleEl.textContent = `采购计划管理 - ${dummyData.planProjectName}`;

                // Display values as plain text with element existence check
                const planNumberEl = document.getElementById('planNumber');
                if (planNumberEl) planNumberEl.textContent = dummyData.planNumber;
                
                const planProjectNameEl = document.getElementById('planProjectName');
                if (planProjectNameEl) planProjectNameEl.textContent = dummyData.planProjectName;
                
                const auditStatusEl = document.getElementById('auditStatus');
                if (auditStatusEl) auditStatusEl.textContent = dummyData.auditStatus;
                
                const procurementTypeEl = document.getElementById('procurementType');
                if (procurementTypeEl) procurementTypeEl.textContent = dummyData.procurementType;
                
                const procurementMethodEl = document.getElementById('procurementMethod');
                if (procurementMethodEl) procurementMethodEl.textContent = dummyData.procurementMethod;
                
                const biddingCategoryEl = document.getElementById('biddingCategory');
                if (biddingCategoryEl) biddingCategoryEl.textContent = dummyData.biddingCategory;
                
                const budgetAmountEl = document.getElementById('budgetAmount');
                if (budgetAmountEl) budgetAmountEl.textContent = dummyData.budgetAmount;
                
                const fundSourceEl = document.getElementById('fundSource');
                if (fundSourceEl) fundSourceEl.textContent = dummyData.fundSource;
                
                const biddingTimeEl = document.getElementById('biddingTime');
                if (biddingTimeEl) biddingTimeEl.textContent = dummyData.biddingTime;
                
                const procurementOrganizationMethodEl = document.getElementById('procurementOrganizationMethod');
                if (procurementOrganizationMethodEl) procurementOrganizationMethodEl.textContent = dummyData.procurementOrganizationMethod;
                
                const agencyEl = document.getElementById('agency');
                if (agencyEl) agencyEl.textContent = dummyData.agency.join(', ');
                
                const annualProcurementPlanEl = document.getElementById('annualProcurementPlan');
                if (annualProcurementPlanEl) annualProcurementPlanEl.textContent = dummyData.annualProcurementPlan;
                
                const projectHandlerEl = document.getElementById('projectHandler');
                if (projectHandlerEl) projectHandlerEl.textContent = dummyData.projectHandler;
                
                const decisionDateEl = document.getElementById('decisionDate');
                if (decisionDateEl) decisionDateEl.textContent = dummyData.decisionDate;
                
                const rejectionReasonEl = document.getElementById('rejectionReason');
                if (rejectionReasonEl) rejectionReasonEl.textContent = dummyData.rejectionReason;
                
                const projectTypeEl = document.getElementById('projectType');
                if (projectTypeEl) projectTypeEl.textContent = dummyData.projectType;
                
                const projectOwnerEl = document.getElementById('projectOwner');
                if (projectOwnerEl) projectOwnerEl.textContent = dummyData.projectOwner;
                
                const projectBasicInfoEl = document.getElementById('projectBasicInfo');
                if (projectBasicInfoEl) projectBasicInfoEl.textContent = dummyData.projectBasicInfo;
                
                const secondaryCompanyEl = document.getElementById('secondaryCompany');
                if (secondaryCompanyEl) secondaryCompanyEl.textContent = dummyData.secondaryCompany;
                
                const remarksEl = document.getElementById('remarks');
                if (remarksEl) remarksEl.textContent = dummyData.remarks;
                
                const decisionFilesEl = document.getElementById('decisionFiles');
                if (decisionFilesEl) decisionFilesEl.textContent = dummyData.decisionFiles;
        }

        // Initialize functions with iframe compatibility
        document.addEventListener('DOMContentLoaded', function() {
            initTabSwitching();
            initPageData();
        });

        // Also try immediate initialization if DOM is already loaded
        if (document.readyState !== 'loading') {
            initTabSwitching();
            initPageData();
        }

    </script>
</body>

</html>