## 生成Funi管理系统页面提示词

### 任务概述

根据业务需求描述，生成完整的页面文件（HTML），包括列表页面、新增/编辑页面和详情/审核页面。页面需要与现有的路由系统完全兼容，确保路径一致性。

**指令格式**: `生成页面-[模块名称或菜单名称]`

**生成范围**:
- **列表页面**: `list.html` - 数据展示、搜索、分页
- **新增/编辑页面**: `add-edit.html` - 表单录入、数据编辑  
- **详情/审核页面**: `detail-review.html` - 数据查看、审核操作

**文件位置**: 所有页面文件生成在用户指定目录下的`pages/`目录下，目录结构需要与现有路由系统保持一致。

### 执行流程（必须按顺序执行）

#### 步骤1：路由配置检查（必须严格执行）
- **查看配置文件**: 检查用户指定目录下的 `index.html` 文件
- **提取路由信息**: 找到目标菜单的实际路由配置（href属性值）
- **确认basePath**: 从href值中提取basePath（去掉#/前缀）
- **验证存在性**: 确认目标菜单在主导航配置中存在
- **示例**: href="#/procurement-plan" → basePath为 "procurement-plan"
- **路径验证**: 确保生成的文件路径为 `pages/{basePath}/list.html`
- **停止条件**: 如果找不到对应菜单，立即停止执行并报告错误
- **禁止推测**: 严禁基于用户输入推测路径，必须从实际配置中提取

#### 步骤2：模板文件验证（必须严格执行）
- **检查模板目录**: 查看 `pm/tools/framework/templates/` 目录
- **确认模板文件**: 验证以下模板文件是否存在：
  - `list-page-template.html`
  - `form-page-template.html` 
  - `detail-page-template.html`
- **停止条件**: 如果模板文件不存在，立即停止执行并报告错误

#### 步骤3：页面生成（必须严格执行）
- **构建目标路径**: 根据步骤1的basePath构建页面文件路径
- **目录结构**: 用户指定目录下的`pages/{basePath}/`
- **文件命名**: 使用固定文件名（list.html, add-edit.html, detail-review.html）
- **生成页面**: 基于模板生成对应的页面文件

#### 步骤4：验证访问（必须严格执行）
- **路径验证**: 确保生成的文件路径与路由配置完全匹配
- **功能测试**: 通过浏览器访问页面并确认功能正常
- **错误检查**: 确保浏览器控制台无JavaScript错误

### 页面生成规范

#### 路由一致性要求
- **严格匹配**: 页面目录路径必须与菜单路由配置完全对应
- **路由解析**: 路由器通过 `pages/{basePath}/{pageFileName}` 加载页面
- **页面路径规则**:
  - 列表页面: `#{basePath}` → `pages/{basePath}/list.html`
  - 新增/编辑页面: `#{basePath}/add-edit` → `pages/{basePath}/add-edit.html`
  - 详情/审核页面: `#{basePath}/detail-review?review=1` → `pages/{basePath}/detail-review.html`

#### 文件命名规则
- **目录名**: 使用从主导航配置中提取的 `basePath`
- **文件名**: 固定为 `list.html`、`add-edit.html`、`detail-review.html`
- **路径示例**: `pages/{basePath}/list.html`

#### 通用要求
- **原型性质**: 生成的页面仅为原型，展示结构和基本功能
- **技术栈**: 使用原生HTML、CSS和JavaScript，禁止使用前端框架
- **页面结构**: 独立HTML文件，不包含整体布局，在iframe中加载
- **主体容器**: 使用 `<div id="app" class="container">` 作为主容器
- **资源引用**: 在路由模式下，所有CSS和JS文件已在主页面(index.html)中统一加载
- **禁止重复引用**: 生成的页面文件不应包含任何CSS和JS文件的引用链接
- **注释说明**: 在页面head部分添加注释说明CSS文件已在主页面统一加载
- **页面跳转**: 使用 `window.top.location.hash` 并严格遵循主导航配置中的路径
- **iframe兼容性**: 必须使用iframe兼容的JavaScript和CSS架构
- **CSS架构规范**: 严格遵循CSS组织规范，业务样式写在页面内部

#### 页面类型详细要求

**1. 列表页面** (`list.html`):
- **模板**: 使用 `pm/tools/framework/templates/list-page-template.html`
- **操作列要求**: 必须包含详情、编辑、删除、审核（如适用）按钮并实现按钮对应的点击逻辑
- **⚠️ 重要：操作列按钮样式**:
  - ✅ 正确：`class="button text"`
  - ❌ 错误：`class="funi-btn funi-btn-text"` 或 `class="button"` 或 `class="btn"`
  - 操作列按钮必须使用文本按钮样式 `class="button text"`，不能使用带边框的按钮
- **CSS类名规范**: 操作列按钮使用 `button text` 类名，其他按钮可使用框架定义的CSS类名

**2. 新增/编辑页面** (`add-edit.html`):
- **模板**: 使用 `pm/tools/framework/templates/form-page-template.html`
- **表单页面**: 支持数据录入和编辑

**3. 详情/审核页面** (`detail-review.html`):
- **模板**: 使用 `pm/tools/framework/templates/detail-page-template.html`
- **只读展示**: 支持审核操作
- **tab切换要求**: 必须使用iframe兼容的JavaScript初始化模式

#### iframe兼容的JavaScript架构

**状态管理规范**：
```javascript
// ✅ 正确：使用window对象避免变量重复声明
window.pageState = window.pageState || {
    isInitialized: false,
    currentStep: 1,
    currentTab: 'basic-info',
    currentId: null
};

// 状态重置函数
function resetPageState() {
    window.pageState.isInitialized = false;
    window.pageState.currentStep = 1;
    window.pageState.currentTab = 'basic-info';
    window.pageState.currentId = null;
}

// ❌ 错误：直接声明全局变量（会导致重复声明错误）
let isInitialized = false;
let currentStep = 1;
```

**页面初始化模式**：
```javascript
function initPageData() {
    // 每次进入页面时重置状态，避免缓存问题
    resetPageState();
    window.pageState.isInitialized = true;

    // 获取URL参数
    const hash = window.top.location.hash;
    const queryString = hash.split('?')[1] || '';
    const urlParams = new URLSearchParams(queryString);
    window.pageState.currentId = urlParams.get('id');

    // 初始化页面功能
    initTabSwitching();
    loadPageData();
}

// 多种初始化时机确保兼容性
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageData);
} else {
    initPageData();
}
window.addEventListener('load', initPageData);
setTimeout(initPageData, 100);
```

#### CSS架构规范

**样式组织原则**：
- **模板样式**：写在assets/css/目录，供模板文件使用
- **业务样式**：必须写在HTML页面内部的`<style>`标签中
- **禁止行为**：不要在assets/css/目录添加业务特定样式
- **CSS类名规范**：页面布局容器必须使用框架定义的CSS类名，避免自定义类名导致样式失效

**框架CSS类名规范**：
- **按钮类名**：`button`（基础）+ `button primary`（主要）/ `button text`（文本）/ `button danger`（危险）
- **容器类名**：`container`、`container-header`、`container-table`
- **搜索类名**：`search-area`、`search-form`、`search-form-item`
- **表格类名**：`data-table`、`table-container`
- **操作类名**：`action-buttons`

**业务页面样式结构**：
```html
<head>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->

    <!-- 业务页面专用样式 -->
    <style>
        /* 页面特定样式写在这里 */
        .operation-log {
            padding: 16px 0;
        }

        .log-item {
            background: #ffffff;
            border: 1px solid #DCDFE6;
            border-radius: 8px;
            padding: 16px;
        }
    </style>
</head>
```

**iframe环境CSS兼容**：
```html
<!-- 当CSS类样式在iframe中不生效时，使用内联样式 -->
<div style="background: #ffffff; border: 1px solid #DCDFE6; padding: 16px;">
    <span style="color: #007FFF; font-weight: 600;">操作标签</span>
</div>
```

### 检查与验证

#### 执行前检查
- **停止执行条件**: 如果以下任一条件不满足，必须立即停止执行：
  1. 无法在index.html中找到目标菜单的路由配置
  2. 模板文件不存在或无法访问
  3. 目标目录无法创建或访问
- **错误报告**: 遇到停止条件时，必须明确报告具体的错误原因
- **禁止推测**: 严禁基于推测或假设生成页面文件路径

#### 路由一致性检查
- **路径匹配**: 验证生成的页面目录路径与主导航配置完全一致
- **跳转链接**: 检查所有 `window.top.location.hash` 跳转使用正确的basePath
- **文件位置**: 确认页面文件创建在正确的目录结构下
- **内部导航**: 验证页面间跳转链接（新增、编辑、详情、返回）路径正确
- **参数传递**: 确保详情页面的参数格式符合路由规范
- **访问测试**: 生成完成后必须验证页面是否可以通过正确的URL访问

#### 表单页面检查
- **第一步active类**: 确保第一个 `.form-step` 和 `.form-step-content` 都有 `active` 类
- **步骤内容对应**: 确保每个 `.form-step-content` 都有对应的 `form-step-X` 类名
- **按钮区域**: 确保 `.form-actions` 有 `id="formActions"` 属性
- **静态按钮禁止**: 严格检查HTML中不能包含任何静态的导航按钮，必须由JavaScript动态生成
- **按钮区域清空**: 确保 `formActions` 容器在HTML中为空，所有按钮由updateButtons函数动态填充

#### 资源引用检查
- **CSS和JS路径**: 检查所有CSS和JS文件的相对路径是否正确
- **select标签**: 禁止使用 `multiple` 属性，如需多选在 `<option value="">` 中添加"(多选)"提示

#### CSS架构检查
- **样式组织**: 业务页面的特定样式必须写在HTML页面内部的`<style>`标签中
- **禁止行为**: 严禁在assets/css/目录添加业务特定样式
- **iframe兼容**: 如果CSS类样式在iframe环境中不生效，必须使用内联样式
- **样式优先级**: 必要时使用`!important`提高样式优先级，确保在iframe环境中生效
- **样式验证**: 生成页面后必须验证样式在iframe环境中的显示效果

#### JavaScript错误检查

**iframe兼容性要求**：
- **变量声明**: 必须使用window对象管理全局状态，避免let/const重复声明错误
- **状态重置**: 每个页面必须包含状态重置函数，在初始化时调用
- **初始化时机**: 提供多种初始化时机确保iframe环境兼容性
- **事件绑定**: 避免事件监听器重复绑定，优先使用onclick属性

**变量管理规范**：
```javascript
// ✅ 正确：使用window对象
window.pageState = window.pageState || {
    isInitialized: false,
    currentStep: 1
};

function resetPageState() {
    window.pageState.isInitialized = false;
    window.pageState.currentStep = 1;
}

// ❌ 错误：直接声明（会导致重复声明错误）
let isInitialized = false;
let currentStep = 1;
```

**初始化模式**：
```javascript
function initPageData() {
    // 每次进入页面时重置状态
    resetPageState();
    window.pageState.isInitialized = true;
    // 初始化逻辑...
}

// 多种初始化时机确保兼容性
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageData);
} else {
    initPageData();
}
window.addEventListener('load', initPageData);
setTimeout(initPageData, 100);
```

**通用检查要求**：
- **全局函数定义位置**: 确保在HTML中直接调用的函数（onclick属性）定义在全局作用域（window对象上），且在DOMContentLoaded事件之前定义
- **事件绑定方式**: 在iframe环境中优先使用onclick属性而非addEventListener，确保动态生成的DOM元素事件绑定稳定
- **CSS类名一致性**: 确保JavaScript中使用的CSS选择器与HTML结构中的实际类名完全匹配
- **DOM重复创建检查**: 在动态创建DOM元素前必须检查元素是否已存在：
  ```javascript
  // 正确示例 - 防重复创建
  const existingFormActions = document.querySelector('.form-actions');
  if (!existingFormActions) {
      const formActions = document.createElement('div');
      formActions.className = 'form-actions';
      mainContent.appendChild(formActions);
  }
  ```

### 技术约束与限制

#### 基础约束
- **禁止使用**: 前端框架（Vue、React）、自定义组件（funi-开头）
- **必须使用**: 原生HTML、CSS、JavaScript
- **页面跳转**: 使用 `window.top.location.hash` 修改顶层窗口hash值
- **路由一致性**: 所有路径必须严格遵循主导航配置中定义的路由结构
- **样式一致性**: 严格遵循模板的样式结构和类名
- **兼容性**: 确保在iframe环境中稳定工作

#### 事件绑定约束
- **全局函数优先**: 所有需要在HTML中调用的函数必须定义为window对象的属性
- **函数定义时机**: 全局函数必须在DOMContentLoaded事件之前定义，确保onclick属性能正确访问
- **事件绑定方式**: 优先使用onclick属性而非addEventListener，特别是对于动态生成的DOM元素
- **iframe环境适配**: 在iframe环境中，事件监听器可能不稳定，onclick属性更可靠

#### 防重复机制
- **防重复初始化**: 确保每个初始化函数在页面生命周期中只被调用一次
- **DOM创建前检查**: 在动态创建任何DOM元素前，必须先检查该元素是否已存在

#### 正确的JavaScript结构示例
```javascript
<script>
    // 1. 全局函数定义 - 必须在DOMContentLoaded之前
    window.viewDetail = function(id) {
        // 动态获取模块路径，避免硬编码
        const hash = window.top.location.hash;
        const pathParts = hash.split('/');
        const moduleName = pathParts[1] || 'default-module';
        window.top.location.hash = `#/${moduleName}/detail-review?id=${id}`;
    };

    window.addNew = function() {
        const hash = window.top.location.hash;
        const pathParts = hash.split('/');
        const moduleName = pathParts[1] || 'default-module';
        window.top.location.hash = `#/${moduleName}/add-edit`;
    };

    // 2. DOM操作函数 - 在DOMContentLoaded内部
    document.addEventListener('DOMContentLoaded', () => {
        // 页面初始化逻辑
    });
</script>
```

#### 按钮HTML示例
```html
<!-- 推荐：使用onclick属性 -->
<button onclick="window.addNew()">新建</button>
<button onclick="window.viewDetail('${id}')">详情</button>

<!-- 避免：使用data属性 + 事件监听器（在iframe环境中可能不稳定） -->
<button data-action="view" data-id="${id}">详情</button>
```

### 成功标准

#### 执行要点
1. **严格按顺序执行**: 查看index.html → 验证模板 → 生成文件 → 验证访问
2. **零容忍推测**: 所有路径信息必须从实际配置文件中获取，严禁推测
3. **立即验证**: 每个步骤完成后立即验证结果，发现问题立即停止

#### 验证标准
- ✅ 页面文件生成在正确的目录结构下
- ✅ 所有资源文件路径正确，页面样式正常显示
- ✅ 页面可以通过正确的URL路径访问
- ✅ 页面间跳转链接工作正常
- ✅ 浏览器控制台无JavaScript错误
- ✅ iframe环境兼容性验证：页面重复访问无错误
- ✅ CSS样式在iframe环境中正确显示
- ✅ JavaScript状态管理正确，无变量重复声明错误

#### iframe环境专项测试
**重复访问测试**：
1. 从列表页面进入详情/编辑页面
2. 返回列表页面
3. 再次进入同一页面
4. 验证无JavaScript错误，功能正常

**样式显示测试**：
1. 检查所有页面元素样式正确显示
2. 特别关注动态生成的内容样式
3. 验证hover效果和交互样式

**最终验证**: 生成完成后，必须通过浏览器访问页面并确认所有功能正常工作，特别是iframe环境下的重复访问稳定性。
