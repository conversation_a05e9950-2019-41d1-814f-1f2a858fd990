<!--
 * @Author: tony10 <EMAIL>
 * @Date: 2025-08-08 13:49:18
 * @LastEditors: tony10 <EMAIL>
 * @LastEditTime: 2025-08-19 22:03:36
 * @FilePath: /docs/prompts/pm/core/form/generate-form.md
 * @Description: 
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
-->
# 生成表单提示词

请根据以下要求生成一个表单：

1.  **布局**: 表单默认采用三列布局展示。
2.  **标签样式**: 表单项的标签（label）背景色为 `#f8f8f8`。请确保此颜色支持深色主题（dark-theme）切换，即在深色模式下能有合适的对应颜色或处理方式。
3.  **样式文件引用与类名强调**:
    - **必须**引用 `docs/pm/prototype/assets/css/funi-form.css` 文件来获取表单和详情页面的样式。
    - **所有表单相关的HTML元素和类名（例如：`form-grid`, `form-section-title`, `form-item-row`, `form-item-full-width`, `input-and-tip-wrapper`, `form-item-tip`, `required`, `form-actions` 等）**必须严格按照 `funi-form.css` 中定义的来使用。**注意：表单的主体容器应使用 `form-grid` 类。**
    - **详情页面相关元素和类名（例如：`detail-grid`, `detail-section-title`, `detail-item-full-width`, `detail-item-value` 等）**也必须严格按照 `funi-form.css` 中定义的来使用。
    - **时间轴相关元素和类名（例如：`timeline-container`, `timeline-item`, `timeline-dot`, `timeline-content`, `timeline-title`, `timeline-meta` 等）**也必须严格按照 `funi-form.css` 中定义的来使用。
4.  **公共样式设置**:
    - **适用范围**: 以下样式设置主要用于 `add-edit.html` 和 `detail-review.html` 页面中的表单。
    - **表单项间距**: 表单项之间建议保持统一的垂直和水平间距，以确保视觉上的整洁和对齐。
    - **输入框/选择器样式**: 统一输入框、选择器等表单控件的边框、圆角和内边距，使其在不同页面中保持一致的用户体验。
    - **必填项标识**: 必填项应有明确的视觉标识（如红色星号），并与标签对齐。
    - **表单标题**: `form-section-title` 元素应放置在 `form` 标签外部。
    - **Select 组件规范**: 关于 Select 组件的生成规范，请参考 [生成Select提示词](generate-select.md)。

**示例表单结构（供参考，具体字段根据需求生成）:**

```json
{
  "formConfig": {
    "layout": "grid",
    "columns": 3,
    "labelStyle": {
      "backgroundColor": "#f8f8f8",
      "darkThemeSupport": true
    },
    "commonStyles": {
      "itemSpacing": "16px",
      "inputBorderRadius": "4px",
      "inputPadding": "8px 12px",
      "requiredIndicatorColor": "red"
    }
  },
  "fields": [
    {
      "label": "字段一",
      "key": "field1",
      "type": "input",
      "placeholder": "请输入字段一",
      "required": true
    },
    {
      "label": "字段二",
      "key": "field2",
      "type": "select",
      "options": [
        { "label": "选项A", "value": "A" },
        { "label": "选项B", "value": "B" }
      ]
    },
    {
      "label": "字段三",
      "key": "field3",
      "type": "date-picker"
    },
    {
      "label": "字段四",
      "key": "field4",
      "type": "textarea",
      "span": 2
    }
  ]
}
```

请生成一个符合上述要求的表单配置或代码片段。
