import fs from 'fs-extra'
import * as path from 'path'
import * as os from 'os'

export interface PMConfig {
  prdFilePath?: string
  outputDirectory?: string
  lastUsedPrompts?: string[]
  userPreferences?: {
    language?: 'zh-CN' | 'en-US'
    theme?: 'light' | 'dark'
  }
}

export class ConfigManager {
  private configPath: string
  private config: PMConfig

  constructor() {
    // 配置文件存储在用户主目录下的 .funi-mcp 文件夹中
    const configDir = path.join(os.homedir(), '.funi-mcp')
    this.configPath = path.join(configDir, 'pm-config.json')
    this.config = {}
  }

  /**
   * 加载配置文件
   */
  async loadConfig(): Promise<PMConfig> {
    try {
      if (await fs.pathExists(this.configPath)) {
        const configData = await fs.readFile(this.configPath, 'utf-8')
        this.config = JSON.parse(configData)
      } else {
        // 如果配置文件不存在，创建默认配置
        this.config = {
          userPreferences: {
            language: 'zh-CN',
            theme: 'light'
          }
        }
        await this.saveConfig()
      }
      return this.config
    } catch (error) {
      throw new Error(`加载配置文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 保存配置文件
   */
  async saveConfig(): Promise<void> {
    try {
      // 确保配置目录存在
      await fs.ensureDir(path.dirname(this.configPath))
      
      // 保存配置文件
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2), 'utf-8')
    } catch (error) {
      throw new Error(`保存配置文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 设置 PRD 文件路径
   */
  async setPrdFilePath(filePath: string): Promise<void> {
    // 验证文件是否存在
    if (!await fs.pathExists(filePath)) {
      throw new Error(`PRD 文件不存在: ${filePath}`)
    }

    this.config.prdFilePath = filePath
    await this.saveConfig()
  }

  /**
   * 设置输出目录
   */
  async setOutputDirectory(directory: string): Promise<void> {
    // 确保目录存在，如果不存在则创建
    await fs.ensureDir(directory)
    
    this.config.outputDirectory = directory
    await this.saveConfig()
  }

  /**
   * 获取 PRD 文件路径
   */
  getPrdFilePath(): string | undefined {
    return this.config.prdFilePath
  }

  /**
   * 获取输出目录
   */
  getOutputDirectory(): string | undefined {
    return this.config.outputDirectory
  }

  /**
   * 添加最近使用的提示词
   */
  async addRecentPrompt(promptPath: string): Promise<void> {
    if (!this.config.lastUsedPrompts) {
      this.config.lastUsedPrompts = []
    }

    // 移除重复项
    this.config.lastUsedPrompts = this.config.lastUsedPrompts.filter(p => p !== promptPath)
    
    // 添加到开头
    this.config.lastUsedPrompts.unshift(promptPath)
    
    // 只保留最近的 10 个
    this.config.lastUsedPrompts = this.config.lastUsedPrompts.slice(0, 10)
    
    await this.saveConfig()
  }

  /**
   * 获取最近使用的提示词
   */
  getRecentPrompts(): string[] {
    return this.config.lastUsedPrompts || []
  }

  /**
   * 设置用户偏好
   */
  async setUserPreference(key: keyof NonNullable<PMConfig['userPreferences']>, value: any): Promise<void> {
    if (!this.config.userPreferences) {
      this.config.userPreferences = {}
    }

    this.config.userPreferences[key] = value
    await this.saveConfig()
  }

  /**
   * 获取用户偏好
   */
  getUserPreference(key: keyof NonNullable<PMConfig['userPreferences']>): any {
    return this.config.userPreferences?.[key]
  }

  /**
   * 检查配置是否完整
   */
  isConfigComplete(): boolean {
    return !!(this.config.prdFilePath && this.config.outputDirectory)
  }

  /**
   * 获取配置状态信息
   */
  getConfigStatus(): {
    hasPrdFile: boolean
    hasOutputDir: boolean
    prdFilePath?: string
    outputDirectory?: string
    isComplete: boolean
  } {
    return {
      hasPrdFile: !!this.config.prdFilePath,
      hasOutputDir: !!this.config.outputDirectory,
      prdFilePath: this.config.prdFilePath,
      outputDirectory: this.config.outputDirectory,
      isComplete: this.isConfigComplete()
    }
  }

  /**
   * 重置配置
   */
  async resetConfig(): Promise<void> {
    this.config = {
      userPreferences: {
        language: 'zh-CN',
        theme: 'light'
      }
    }
    await this.saveConfig()
  }
}
