# MCP服务配置
NODE_ENV=production
MCP_PORT=3000
MCP_ENDPOINT=/mcp

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/mcp.log

# 安全配置
API_KEY=your-secret-api-key
CORS_ORIGIN=*
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 数据库配置（如果需要）
# DATABASE_URL=postgresql://user:password@localhost:5432/mcp_db

# Redis配置（如果需要缓存）
# REDIS_URL=redis://localhost:6379

# 监控配置
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# SSL配置（如果使用HTTPS）
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
