import{defineCommand as ou,runMain as su}from"citty";import{createServer as au}from"node:http";import{RestServerTransport as iu}from"@chatmcp/sdk/server/rest.js";import{McpServer as cu}from"@modelcontextprotocol/sdk/server/mcp.js";import{SSEServerTransport as Bu}from"@modelcontextprotocol/sdk/server/sse.js";import{StdioServerTransport as Au}from"@modelcontextprotocol/sdk/server/stdio.js";import{createApp as Du,createRouter as Cu,defineEventHandler as k,getQuery as lu,setResponseStatus as pu,toNodeListener as du}from"h3";import mu from"fs";import gu from"path";import fu from"os";import hu from"crypto";import{z as y}from"zod";import{ResourceManager as yu}from"./pm/resource-manager.mjs";import{AutoSessionManager as M}from"./pm/auto-session-manager.mjs";import j from"fs-extra";import"url";import"./pm/session-config-manager.mjs";const L="0.0.1";function $u(o){const{name:F,version:n}=o;return new cu({name:F,version:n})}async function V(o,F={type:"stdio"}){if(F.type==="stdio"){const r=new Au;await o.connect(r);return}if(F.type==="http"){const r=F.port??3e3,m=F.endpoint??"/mcp",l=new iu({port:r,endpoint:m});await o.connect(l),await l.startServer(),console.log(`HTTP server listening \u2192 http://localhost:${r}${m}`);return}const n=F.port??3e3,E=new Map,i=Du(),a=Cu();a.get("/sse",k(async r=>{const m=r.node.res,l=new Bu("/messages",m);E.set(l.sessionId,l),m.on("close",()=>E.delete(l.sessionId)),await o.connect(l)})),a.post("/messages",k(async r=>{const{sessionId:m}=lu(r),l=m?E.get(m):void 0;if(l)await l.handlePostMessage(r.node.req,r.node.res);else return pu(r,400),"No transport found for sessionId"})),i.use(a),au(du(i)).listen(n),console.log(`SSE server listening \u2192 http://localhost:${n}/sse`)}async function U(o){try{await o.close()}catch(F){console.error("Error occurred during server stop:",F)}finally{process.exit(0)}}function vu(o){if(Object.prototype.hasOwnProperty.call(o,"__esModule"))return o;var F=o.default;if(typeof F=="function"){var n=function E(){var i=!1;try{i=this instanceof E}catch{}return i?Reflect.construct(F,arguments,this.constructor):F.apply(this,arguments)};n.prototype=F.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(o).forEach(function(E){var i=Object.getOwnPropertyDescriptor(o,E);Object.defineProperty(n,E,i.get?i:{enumerable:!0,get:function(){return o[E]}})}),n}var T={exports:{}};const q="dotenv",Y="16.6.1",H="Loads environment variables from .env file",K="lib/main.js",X="lib/main.d.ts",J={".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},G={"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},Q={type:"git",url:"git://github.com/motdotla/dotenv.git"},W="https://github.com/motdotla/dotenv#readme",z="https://dotenvx.com",Z=["dotenv","env",".env","environment","variables","config","settings"],uu="README.md",tu="BSD-2-Clause",eu={"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},Fu={node:">=12"},ru={fs:!1},xu={name:q,version:Y,description:H,main:K,types:X,exports:J,scripts:G,repository:Q,homepage:W,funding:z,keywords:Z,readmeFilename:uu,license:tu,devDependencies:eu,engines:Fu,browser:ru},wu={__proto__:null,browser:ru,default:xu,description:H,devDependencies:eu,engines:Fu,exports:J,funding:z,homepage:W,keywords:Z,license:tu,main:K,name:q,readmeFilename:uu,repository:Q,scripts:G,types:X,version:Y},bu=vu(wu);var Eu;function Pu(){if(Eu)return T.exports;Eu=1;const o=mu,F=gu,n=fu,E=hu,a=bu.version,r=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function m(u){const D={};let C=u.toString();C=C.replace(/\r\n?/mg,`
`);let g;for(;(g=r.exec(C))!=null;){const h=g[1];let c=g[2]||"";c=c.trim();const d=c[0];c=c.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),d==='"'&&(c=c.replace(/\\n/g,`
`),c=c.replace(/\\r/g,"\r")),D[h]=c}return D}function l(u){u=u||{};const D=v(u);u.path=D;const C=t.configDotenv(u);if(!C.parsed){const d=new Error(`MISSING_DATA: Cannot parse ${D} for an unknown reason`);throw d.code="MISSING_DATA",d}const g=P(u).split(","),h=g.length;let c;for(let d=0;d<h;d++)try{const f=g[d].trim(),S=B(C,f);c=t.decrypt(S.ciphertext,S.key);break}catch(f){if(d+1>=h)throw f}return t.parse(c)}function x(u){console.log(`[dotenv@${a}][WARN] ${u}`)}function p(u){console.log(`[dotenv@${a}][DEBUG] ${u}`)}function $(u){console.log(`[dotenv@${a}] ${u}`)}function P(u){return u&&u.DOTENV_KEY&&u.DOTENV_KEY.length>0?u.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function B(u,D){let C;try{C=new URL(D)}catch(f){if(f.code==="ERR_INVALID_URL"){const S=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw S.code="INVALID_DOTENV_KEY",S}throw f}const g=C.password;if(!g){const f=new Error("INVALID_DOTENV_KEY: Missing key part");throw f.code="INVALID_DOTENV_KEY",f}const h=C.searchParams.get("environment");if(!h){const f=new Error("INVALID_DOTENV_KEY: Missing environment part");throw f.code="INVALID_DOTENV_KEY",f}const c=`DOTENV_VAULT_${h.toUpperCase()}`,d=u.parsed[c];if(!d){const f=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${c} in your .env.vault file.`);throw f.code="NOT_FOUND_DOTENV_ENVIRONMENT",f}return{ciphertext:d,key:g}}function v(u){let D=null;if(u&&u.path&&u.path.length>0)if(Array.isArray(u.path))for(const C of u.path)o.existsSync(C)&&(D=C.endsWith(".vault")?C:`${C}.vault`);else D=u.path.endsWith(".vault")?u.path:`${u.path}.vault`;else D=F.resolve(process.cwd(),".env.vault");return o.existsSync(D)?D:null}function w(u){return u[0]==="~"?F.join(n.homedir(),u.slice(1)):u}function _(u){const D=!!(u&&u.debug),C=u&&"quiet"in u?u.quiet:!0;(D||!C)&&$("Loading env from encrypted .env.vault");const g=t._parseVault(u);let h=process.env;return u&&u.processEnv!=null&&(h=u.processEnv),t.populate(h,g,u),{parsed:g}}function I(u){const D=F.resolve(process.cwd(),".env");let C="utf8";const g=!!(u&&u.debug),h=u&&"quiet"in u?u.quiet:!0;u&&u.encoding?C=u.encoding:g&&p("No encoding is specified. UTF-8 is used by default");let c=[D];if(u&&u.path)if(!Array.isArray(u.path))c=[w(u.path)];else{c=[];for(const N of u.path)c.push(w(N))}let d;const f={};for(const N of c)try{const b=t.parse(o.readFileSync(N,{encoding:C}));t.populate(f,b,u)}catch(b){g&&p(`Failed to load ${N} ${b.message}`),d=b}let S=process.env;if(u&&u.processEnv!=null&&(S=u.processEnv),t.populate(S,f,u),g||!h){const N=Object.keys(f).length,b=[];for(const O of c)try{const R=F.relative(process.cwd(),O);b.push(R)}catch(R){g&&p(`Failed to load ${O} ${R.message}`),d=R}$(`injecting env (${N}) from ${b.join(",")}`)}return d?{parsed:f,error:d}:{parsed:f}}function A(u){if(P(u).length===0)return t.configDotenv(u);const D=v(u);return D?t._configVault(u):(x(`You set DOTENV_KEY but you are missing a .env.vault file at ${D}. Did you forget to build it?`),t.configDotenv(u))}function s(u,D){const C=Buffer.from(D.slice(-64),"hex");let g=Buffer.from(u,"base64");const h=g.subarray(0,12),c=g.subarray(-16);g=g.subarray(12,-16);try{const d=E.createDecipheriv("aes-256-gcm",C,h);return d.setAuthTag(c),`${d.update(g)}${d.final()}`}catch(d){const f=d instanceof RangeError,S=d.message==="Invalid key length",N=d.message==="Unsupported state or unable to authenticate data";if(f||S){const b=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw b.code="INVALID_DOTENV_KEY",b}else if(N){const b=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw b.code="DECRYPTION_FAILED",b}else throw d}}function e(u,D,C={}){const g=!!(C&&C.debug),h=!!(C&&C.override);if(typeof D!="object"){const c=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw c.code="OBJECT_REQUIRED",c}for(const c of Object.keys(D))Object.prototype.hasOwnProperty.call(u,c)?(h===!0&&(u[c]=D[c]),g&&p(h===!0?`"${c}" is already defined and WAS overwritten`:`"${c}" is already defined and was NOT overwritten`)):u[c]=D[c]}const t={configDotenv:I,_configVault:_,_parseVault:l,config:A,decrypt:s,parse:m,populate:e};return T.exports.configDotenv=t.configDotenv,T.exports._configVault=t._configVault,T.exports._parseVault=t._parseVault,T.exports.config=t.config,T.exports.decrypt=t.decrypt,T.exports.parse=t.parse,T.exports.populate=t.populate,T.exports=t,T.exports}var nu=Pu();nu.config();function _u(o){const F={};try{const E=JSON.parse(o);return E.productRequirement&&(F.productRequirement=E.productRequirement),E.functionalModules&&(F.functionalModules=E.functionalModules),E.technicalConstraints&&(F.technicalConstraints=E.technicalConstraints),E.fileContent&&(F.fileContent=E.fileContent),E.fileName&&(F.fileName=E.fileName),F}catch{}const n=o.split(`
`).map(E=>E.trim()).filter(E=>E);for(const E of n){const i=E.toLowerCase();(i.includes("\u4EA7\u54C1\u9700\u6C42")||i.includes("prd")||i.includes("\u9700\u6C42\u6587\u6863")||i.includes("product requirement"))&&(F.productRequirement=E.replace(/^[^：:]*[：:]?\s*/,"")),(i.includes("\u529F\u80FD\u6A21\u5757")||i.includes("\u6A21\u5757\u6E05\u5355")||i.includes("functional modules")||i.includes("\u529F\u80FD\u6E05\u5355"))&&(F.functionalModules=E.replace(/^[^：:]*[：:]?\s*/,"")),(i.includes("\u6280\u672F\u7EA6\u675F")||i.includes("\u7EA6\u675F\u6761\u4EF6")||i.includes("technical constraints")||i.includes("\u6280\u672F\u9650\u5236"))&&(F.technicalConstraints=E.replace(/^[^：:]*[：:]?\s*/,""))}return!F.productRequirement&&!F.functionalModules&&!F.technicalConstraints&&(F.productRequirement=o),F}function Su(o){const F=[],n=[];return o.productRequirement||(F.push("\u4EA7\u54C1\u9700\u6C42"),n.push("\u8BF7\u63D0\u4F9B\u4EA7\u54C1\u9700\u6C42\u6587\u6863\uFF08PRD\uFF09\u5185\u5BB9\u6216\u4E0A\u4F20\u76F8\u5173\u6587\u4EF6")),o.functionalModules||(F.push("\u529F\u80FD\u6A21\u5757"),n.push("\u8BF7\u63D0\u4F9B\u529F\u80FD\u6A21\u5757\u6E05\u5355\u6216\u8BE6\u7EC6\u7684\u529F\u80FD\u63CF\u8FF0")),o.technicalConstraints||(F.push("\u6280\u672F\u7EA6\u675F"),n.push("\u8BF7\u63CF\u8FF0\u6280\u672F\u7EA6\u675F\u6761\u4EF6\uFF08\u5982\u6280\u672F\u6808\u3001\u6027\u80FD\u8981\u6C42\u3001\u517C\u5BB9\u6027\u7B49\uFF09")),{isComplete:F.length===0,missingParams:F,promptQuestions:n}}function Tu(o){return`# \u6280\u672F\u65B9\u6848\u8BBE\u8BA1AI\u52A9\u624B

## \u89D2\u8272\u5B9A\u4E49
\u4F60\u662F\u4E00\u540D\u8D44\u6DF1\u7684\u524D\u7AEF\u67B6\u6784\u5E08\uFF0C\u5177\u6709\u4E30\u5BCC\u7684Vue3\u3001\u4F4E\u4EE3\u7801\u5E73\u53F0\u548C\u4F01\u4E1A\u7EA7\u5E94\u7528\u5F00\u53D1\u7ECF\u9A8C\u3002

## \u4EFB\u52A1\u76EE\u6807
\u57FA\u4E8E\u4EE5\u4E0B\u8F93\u5165\u4FE1\u606F\uFF0C\u8BBE\u8BA1\u5B8C\u6574\u7684\u6280\u672F\u65B9\u6848\u548C\u7CFB\u7EDF\u67B6\u6784\u3002

## \u8F93\u5165\u4FE1\u606F
**\u4EA7\u54C1\u9700\u6C42\uFF1A** ${o.productRequirement||"\u5F85\u8865\u5145"}
**\u529F\u80FD\u6A21\u5757\uFF1A** ${o.functionalModules||"\u5F85\u8865\u5145"}
**\u6280\u672F\u7EA6\u675F\uFF1A** ${o.technicalConstraints||"\u5F85\u8865\u5145"}
${o.fileName?`**\u4E0A\u4F20\u6587\u4EF6\uFF1A** ${o.fileName}`:""}
${o.fileContent?`**\u6587\u4EF6\u5185\u5BB9\uFF1A**
\`\`\`
${o.fileContent}
\`\`\``:""}

## \u6280\u672F\u6808\u7EA6\u675F
- \u524D\u7AEF\uFF1AVue3 + Element Plus + Pinia + Vue Router
- \u6784\u5EFA\u5DE5\u5177\uFF1AVite
- \u4EE3\u7801\u89C4\u8303\uFF1AESLint + Prettier
- \u540E\u7AEF\u63A5\u53E3\uFF1A\u57FA\u4E8E\u4F4E\u4EE3\u7801\u5E73\u53F0\u7684RESTful API
- \u90E8\u7F72\uFF1ADocker + Nginx

## \u8BBE\u8BA1\u539F\u5219
1. \u7B80\u5355\u4F18\u4E8E\u590D\u6742\uFF1A\u9009\u62E9\u6700\u7B80\u5355\u53EF\u884C\u7684\u6280\u672F\u65B9\u6848
2. \u4E00\u81F4\u6027\u4F18\u5148\uFF1A\u786E\u4FDD\u4EE3\u7801\u98CE\u683C\u548C\u67B6\u6784\u6A21\u5F0F\u7EDF\u4E00
3. \u53EF\u7EF4\u62A4\u6027\uFF1A\u4EE3\u7801\u6613\u4E8E\u7406\u89E3\u3001\u4FEE\u6539\u548C\u6269\u5C55
4. \u6027\u80FD\u8003\u8651\uFF1A\u907F\u514D\u8FC7\u5EA6\u6E32\u67D3\u548C\u4E0D\u5FC5\u8981\u7684\u7F51\u7EDC\u8BF7\u6C42

## \u8F93\u51FA\u8981\u6C42

### 1. \u7CFB\u7EDF\u67B6\u6784\u8BBE\u8BA1
- \u6574\u4F53\u67B6\u6784\u56FE\u548C\u8BF4\u660E
- \u6A21\u5757\u4F9D\u8D56\u5173\u7CFB\u56FE
- \u6570\u636E\u6D41\u5411\u8BBE\u8BA1
- \u72B6\u6001\u7BA1\u7406\u65B9\u6848\uFF08Pinia stores\u8BBE\u8BA1\uFF09

### 2. \u524D\u7AEF\u5DE5\u7A0B\u7ED3\u6784
\u8BF7\u63D0\u4F9B\u8BE6\u7EC6\u7684\u76EE\u5F55\u7ED3\u6784\uFF1A
\`\`\`
src/
\u251C\u2500\u2500 components/     # \u516C\u5171\u7EC4\u4EF6
\u2502   \u251C\u2500\u2500 common/     # \u901A\u7528\u7EC4\u4EF6
\u2502   \u2514\u2500\u2500 business/   # \u4E1A\u52A1\u7EC4\u4EF6
\u251C\u2500\u2500 views/         # \u9875\u9762\u7EC4\u4EF6
\u251C\u2500\u2500 api/           # API\u8C03\u7528\u5C01\u88C5
\u251C\u2500\u2500 stores/        # Pinia\u72B6\u6001\u7BA1\u7406
\u251C\u2500\u2500 utils/         # \u5DE5\u5177\u51FD\u6570
\u251C\u2500\u2500 composables/   # Vue3\u7EC4\u5408\u5F0F\u51FD\u6570
\u251C\u2500\u2500 router/        # \u8DEF\u7531\u914D\u7F6E
\u251C\u2500\u2500 types/         # TypeScript\u7C7B\u578B\u5B9A\u4E49
\u2514\u2500\u2500 assets/        # \u9759\u6001\u8D44\u6E90
\`\`\`

### 3. \u6838\u5FC3\u7EC4\u4EF6\u8BBE\u8BA1
\u4E3A\u6BCF\u4E2A\u4E3B\u8981\u529F\u80FD\u6A21\u5757\u8BBE\u8BA1\u7EC4\u4EF6\uFF1A
- \u7EC4\u4EF6\u547D\u540D\u89C4\u8303
- \u7EC4\u4EF6\u7ED3\u6784\u6A21\u677F
- Props\u548CEvents\u5B9A\u4E49
- \u6837\u5F0F\u7F16\u5199\u89C4\u8303\uFF08\u4F7F\u7528Element Plus\u4E3B\u9898\uFF09

### 4. API\u96C6\u6210\u65B9\u6848
- \u63A5\u53E3\u8C03\u7528\u5C01\u88C5\uFF08\u57FA\u4E8Eaxios\uFF09
- \u7EDF\u4E00\u9519\u8BEF\u5904\u7406\u673A\u5236
- \u8BF7\u6C42/\u54CD\u5E94\u62E6\u622A\u5668\u8BBE\u8BA1
- \u6570\u636E\u683C\u5F0F\u8F6C\u6362\u548C\u9A8C\u8BC1
- \u63A5\u53E3\u7F13\u5B58\u7B56\u7565

### 5. \u72B6\u6001\u7BA1\u7406\u8BBE\u8BA1
- Pinia stores\u7ED3\u6784\u8BBE\u8BA1
- \u5168\u5C40\u72B6\u6001\u548C\u5C40\u90E8\u72B6\u6001\u5212\u5206
- \u72B6\u6001\u6301\u4E45\u5316\u65B9\u6848
- \u72B6\u6001\u66F4\u65B0\u548C\u540C\u6B65\u673A\u5236

### 6. \u8DEF\u7531\u8BBE\u8BA1
- \u8DEF\u7531\u7ED3\u6784\u548C\u5C42\u7EA7
- \u8DEF\u7531\u5B88\u536B\u548C\u6743\u9650\u63A7\u5236
- \u61D2\u52A0\u8F7D\u548C\u4EE3\u7801\u5206\u5272
- \u8DEF\u7531\u5143\u4FE1\u606F\u914D\u7F6E

### 7. \u6027\u80FD\u4F18\u5316\u7B56\u7565
- \u7EC4\u4EF6\u61D2\u52A0\u8F7D\u65B9\u6848
- \u56FE\u7247\u548C\u8D44\u6E90\u4F18\u5316
- \u6253\u5305\u4F18\u5316\u914D\u7F6E
- \u8FD0\u884C\u65F6\u6027\u80FD\u76D1\u63A7

### 8. \u5F00\u53D1\u89C4\u8303\u548C\u5DE5\u5177\u914D\u7F6E
- ESLint\u548CPrettier\u914D\u7F6E
- Git\u63D0\u4EA4\u89C4\u8303
- \u7EC4\u4EF6\u5F00\u53D1\u6A21\u677F
- \u5355\u5143\u6D4B\u8BD5\u7B56\u7565

## AI\u7EA6\u675F\u548C\u8D28\u91CF\u8981\u6C42
- **\u6280\u672F\u6808\u4E00\u81F4\u6027**\uFF1A\u4E25\u683C\u57FA\u4E8EVue3+Element Plus+\u73B0\u6709\u6846\u67B6\u8FDB\u884C\u8BBE\u8BA1
- **\u7EC4\u4EF6\u590D\u7528\u6027**\uFF1A\u6700\u5927\u5316\u590D\u7528\u73B0\u6709\u7EC4\u4EF6\uFF0C\u907F\u514D\u91CD\u590D\u5F00\u53D1
- **\u67B6\u6784\u7B80\u6D01\u6027**\uFF1A\u907F\u514D\u8FC7\u5EA6\u62BD\u8C61\uFF0C\u91C7\u7528\u6700\u76F4\u63A5\u7684\u5B9E\u73B0\u65B9\u6848
- **\u4EE3\u7801\u4E00\u81F4\u6027**\uFF1A\u786E\u4FDD\u6240\u6709\u7EC4\u4EF6\u9075\u5FAA\u7EDF\u4E00\u7684\u4EE3\u7801\u89C4\u8303\u548C\u6A21\u5F0F
- **\u6027\u80FD\u5B9E\u7528\u6027**\uFF1A\u6027\u80FD\u4F18\u5316\u65B9\u6848\u5FC5\u987B\u5207\u5B9E\u53EF\u884C\uFF0C\u907F\u514D\u8FC7\u5EA6\u4F18\u5316

## \u8D28\u91CF\u68C0\u67E5\u70B9
1. **\u6280\u672F\u6808\u7B26\u5408\u6027\u68C0\u67E5**\uFF1A\u9A8C\u8BC1\u6240\u6709\u6280\u672F\u9009\u578B\u90FD\u5728\u73B0\u6709\u6280\u672F\u6808\u8303\u56F4\u5185
2. **\u7EC4\u4EF6\u590D\u7528\u7387\u8BC4\u4F30**\uFF1A\u68C0\u67E5\u73B0\u6709\u7EC4\u4EF6\u7684\u590D\u7528\u7A0B\u5EA6\uFF0C\u907F\u514D\u91CD\u590D\u5F00\u53D1
3. **\u67B6\u6784\u5408\u7406\u6027\u5BA1\u67E5**\uFF1A\u786E\u4FDD\u67B6\u6784\u8BBE\u8BA1\u7B80\u6D01\u660E\u4E86\uFF0C\u6613\u4E8E\u7406\u89E3\u548C\u7EF4\u62A4
4. **\u63A5\u53E3\u4E00\u81F4\u6027\u9A8C\u8BC1**\uFF1A\u68C0\u67E5API\u63A5\u53E3\u8BBE\u8BA1\u7684\u4E00\u81F4\u6027\u548C\u89C4\u8303\u6027
5. **\u6027\u80FD\u65B9\u6848\u53EF\u884C\u6027\u8BC4\u4F30**\uFF1A\u9A8C\u8BC1\u6027\u80FD\u4F18\u5316\u65B9\u6848\u7684\u5B9E\u9645\u6548\u679C\u548C\u5B9E\u65BD\u96BE\u5EA6

\u8BF7\u57FA\u4E8E\u4EE5\u4E0A\u8981\u6C42\uFF0C\u7ED3\u5408\u8F93\u5165\u7684\u4EA7\u54C1\u9700\u6C42\u548C\u529F\u80FD\u6A21\u5757\uFF0C\u8BBE\u8BA1\u51FA\u5B8C\u6574\u7684\u6280\u672F\u65B9\u6848\u3002`}function Nu({mcp:o}){o.tool("technical-proposal",`\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u5DE5\u5177 - \u5E2E\u52A9\u60A8\u8BBE\u8BA1\u5B8C\u6574\u7684\u524D\u7AEF\u6280\u672F\u65B9\u6848\u548C\u7CFB\u7EDF\u67B6\u6784\u3002

\u4F7F\u7528\u65B9\u6CD5\uFF1A
1. \u63D0\u4F9B\u4EA7\u54C1\u9700\u6C42\u6587\u6863\uFF08PRD\uFF09\u5185\u5BB9
2. \u63CF\u8FF0\u529F\u80FD\u6A21\u5757\u6E05\u5355
3. \u8BF4\u660E\u6280\u672F\u7EA6\u675F\u6761\u4EF6
4. \u652F\u6301\u4E0A\u4F20\u76F8\u5173\u6587\u6863\u6587\u4EF6

\u652F\u6301\u8F93\u5165\u683C\u5F0F\uFF1A
- \u81EA\u7136\u8BED\u8A00\u63CF\u8FF0\u5404\u4E2A\u8981\u7D20
- JSON\u683C\u5F0F\u7ED3\u6784\u5316\u8F93\u5165\uFF1A
  {
    "productRequirement": "\u4EA7\u54C1\u9700\u6C42\u63CF\u8FF0",
    "functionalModules": "\u529F\u80FD\u6A21\u5757\u6E05\u5355",
    "technicalConstraints": "\u6280\u672F\u7EA6\u675F\u6761\u4EF6",
    "fileContent": "\u4E0A\u4F20\u6587\u4EF6\u5185\u5BB9",
    "fileName": "\u6587\u4EF6\u540D\u79F0"
  }

\u793A\u4F8B\u8F93\u5165\uFF1A
- "\u4EA7\u54C1\u9700\u6C42\uFF1A\u5F00\u53D1\u4E00\u4E2A\u4F01\u4E1A\u7EA7\u9879\u76EE\u7BA1\u7406\u7CFB\u7EDF\uFF1B\u529F\u80FD\u6A21\u5757\uFF1A\u9879\u76EE\u521B\u5EFA\u3001\u4EFB\u52A1\u5206\u914D\u3001\u8FDB\u5EA6\u8DDF\u8E2A\uFF1B\u6280\u672F\u7EA6\u675F\uFF1A\u57FA\u4E8EVue3+Element Plus"
- \u6216\u76F4\u63A5\u4E0A\u4F20PRD\u6587\u6863\uFF0C\u5DE5\u5177\u4F1A\u81EA\u52A8\u89E3\u6790\u5185\u5BB9`,{userInput:y.string().describe("\u7528\u6237\u8F93\u5165\u7684\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u4FE1\u606F\uFF08\u53EF\u4EE5\u662F\u63CF\u8FF0\u6216\u6587\u4EF6\u5185\u5BB9\uFF09")},async({userInput:F})=>{if(!F||F.trim()==="")return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u4FE1\u606F\u3002\u60A8\u53EF\u4EE5\u63CF\u8FF0\u4EA7\u54C1\u9700\u6C42\u3001\u529F\u80FD\u6A21\u5757\u548C\u6280\u672F\u7EA6\u675F\uFF0C\u6216\u8005\u4E0A\u4F20\u76F8\u5173\u6587\u6863\u3002"}]};try{const n=_u(F.trim()),{isComplete:E,missingParams:i,promptQuestions:a}=Su(n);if(!E){const m=i.join("\u3001"),l=a.map((x,p)=>`${p+1}. ${x}`).join(`
`);return{content:[{type:"text",text:`\u6536\u5230\u60A8\u7684\u4FE1\u606F\uFF0C\u4F46\u8FD8\u9700\u8981\u8865\u5145\u4EE5\u4E0B\u5185\u5BB9\u624D\u80FD\u8FDB\u884C\u5B8C\u6574\u7684\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\uFF1A

\u7F3A\u5931\u4FE1\u606F\uFF1A${m}

\u8BF7\u8865\u5145\u4EE5\u4E0B\u4FE1\u606F\uFF1A
${l}

\u5DF2\u6536\u96C6\u5230\u7684\u4FE1\u606F\uFF1A
${n.productRequirement?`- \u4EA7\u54C1\u9700\u6C42\uFF1A${n.productRequirement}`:""}
${n.functionalModules?`- \u529F\u80FD\u6A21\u5757\uFF1A${n.functionalModules}`:""}
${n.technicalConstraints?`- \u6280\u672F\u7EA6\u675F\uFF1A${n.technicalConstraints}`:""}
${n.fileName?`- \u4E0A\u4F20\u6587\u4EF6\uFF1A${n.fileName}`:""}

\u60A8\u53EF\u4EE5\u7EE7\u7EED\u63D0\u4F9B\u7F3A\u5931\u7684\u4FE1\u606F\uFF0C\u6211\u4F1A\u5E2E\u60A8\u5B8C\u5584\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u3002`}]}}const r=Tu(n);return{content:[{type:"text",text:`\u5DF2\u6536\u96C6\u5B8C\u6574\u7684\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u4FE1\u606F\uFF0C\u6B63\u5728\u4E3A\u60A8\u751F\u6210\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u63D0\u793A\u8BCD\uFF1A

\u6536\u96C6\u5230\u7684\u4FE1\u606F\uFF1A
- \u4EA7\u54C1\u9700\u6C42\uFF1A${n.productRequirement}
- \u529F\u80FD\u6A21\u5757\uFF1A${n.functionalModules}
- \u6280\u672F\u7EA6\u675F\uFF1A${n.technicalConstraints}
${n.fileName?`- \u4E0A\u4F20\u6587\u4EF6\uFF1A${n.fileName}`:""}

\u751F\u6210\u7684\u5B8C\u6574\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u63D0\u793A\u8BCD\uFF1A

${r}`}]}}catch(n){return{isError:!0,content:[{type:"text",text:`\u5904\u7406\u6280\u672F\u65B9\u6848\u8BBE\u8BA1\u4FE1\u606F\u65F6\u51FA\u73B0\u9519\u8BEF\uFF1A${n instanceof Error?n.message:"\u672A\u77E5\u9519\u8BEF"}\u3002\u8BF7\u68C0\u67E5\u8F93\u5165\u683C\u5F0F\u5E76\u91CD\u8BD5\u3002`}]}}})}nu.config();function Iu(){try{const o=path.join(process.cwd(),"UIUX_Designer_Agent_Prompt.md");return fs.existsSync(o)?fs.readFileSync(o,"utf-8"):"\u672A\u627E\u5230 UIUX_Designer_Agent_Prompt.md \u6587\u4EF6\uFF0C\u8BF7\u786E\u4FDD\u6587\u4EF6\u5B58\u5728\u4E8E\u9879\u76EE\u6839\u76EE\u5F55\u3002"}catch(o){return console.error("\u8BFB\u53D6 UIUX_Designer_Agent_Prompt.md \u6587\u4EF6\u5931\u8D25:",o),"\u8BFB\u53D6\u63D0\u793A\u8BCD\u6587\u4EF6\u65F6\u51FA\u73B0\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u662F\u5426\u5B58\u5728\u4E14\u53EF\u8BFB\u3002"}}function Ru({mcp:o}){o.tool("Funi-UI-UE",`UI/UX\u8BBE\u8BA1\u5B9E\u73B0\u5DE5\u5177 - \u8BFB\u53D6\u672C\u5730docs/PRD.md\u548Cdocs/User_Story_Map.md\u6587\u4EF6\uFF0C\u751F\u6210\u7EDF\u4E00\u7684UI/UX\u8BBE\u8BA1\u63D0\u793A\u8BCD\u3002

\u4F7F\u7528\u65B9\u6CD5\uFF1A
- \u5DE5\u5177\u4F1A\u81EA\u52A8\u8BFB\u53D6\u9879\u76EE\u6839\u76EE\u5F55\u4E0B\u7684docs/PRD.md\u548Cdocs/User_Story_Map.md\u6587\u4EF6
- \u7ED3\u5408UIUX_Designer_Agent_Prompt.md\u6A21\u677F\u751F\u6210\u5B8C\u6574\u7684\u8BBE\u8BA1\u63D0\u793A\u8BCD
- \u76F4\u63A5\u8FD4\u56DE\u63D0\u793A\u8BCD\u4F9B\u56E2\u961F\u6210\u5458\u4F7F\u7528

\u6CE8\u610F\uFF1A\u8BF7\u786E\u4FDD\u4EE5\u4E0B\u6587\u4EF6\u5B58\u5728\uFF1A
- docs/PRD.md (\u4EA7\u54C1\u9700\u6C42\u6587\u6863)
- docs/User_Story_Map.md (\u7528\u6237\u6545\u4E8B\u5730\u56FE)
- UIUX_Designer_Agent_Prompt.md (UI/UX\u8BBE\u8BA1\u63D0\u793A\u8BCD\u6A21\u677F)`,{trigger:y.string().optional().describe("\u89E6\u53D1\u5DE5\u5177\u6267\u884C")},async()=>{try{const{prdContent:F,userStoryMap:n}=readLocalDocs();let E=Iu();return F&&(E+=`

## \u4EA7\u54C1\u9700\u6C42\u6587\u6863 (PRD)
${F}`),n&&(E+=`

## \u7528\u6237\u6545\u4E8B\u5730\u56FE
${n}`),{content:[{type:"text",text:E}]}}catch(F){return{isError:!0,content:[{type:"text",text:`\u751F\u6210UI/UX\u8BBE\u8BA1\u63D0\u793A\u8BCD\u65F6\u51FA\u73B0\u9519\u8BEF\uFF1A${F instanceof Error?F.message:"\u672A\u77E5\u9519\u8BEF"}\u3002\u8BF7\u68C0\u67E5\u76F8\u5173\u6587\u4EF6\u662F\u5426\u5B58\u5728\u3002`}]}}})}function Mu({mcp:o}){const F=new yu;o.tool("pm-auto-config",`\u4EA7\u54C1\u7ECF\u7406\u81EA\u52A8\u914D\u7F6E\u7BA1\u7406\u5DE5\u5177 - \u81EA\u52A8\u68C0\u6D4B\u7528\u6237\u73AF\u5883\uFF0C\u65E0\u9700\u624B\u52A8\u6307\u5B9A\u7528\u6237\u4FE1\u606F

\u4F7F\u7528\u65B9\u6CD5\uFF1A
1. \u8BBE\u7F6E PRD \u6587\u4EF6\u8DEF\u5F84\uFF1A{"action": "set-prd", "path": "/path/to/PRD.md"}
2. \u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\uFF1A{"action": "set-output", "path": "/path/to/output"}
3. \u67E5\u770B\u914D\u7F6E\u72B6\u6001\uFF1A{"action": "status"}
4. \u67E5\u770B\u4F1A\u8BDD\u4FE1\u606F\uFF1A{"action": "session-info"}
5. \u8BCA\u65AD\u521D\u59CB\u5316\u95EE\u9898\uFF1A{"action": "diagnose"}

\u6CE8\u610F\uFF1A
- \u4F1A\u81EA\u52A8\u68C0\u6D4B\u7528\u6237\u73AF\u5883\u548C\u9879\u76EE\u8DEF\u5F84\uFF0C\u65E0\u9700\u624B\u52A8\u6307\u5B9A\u4F1A\u8BDDID
- \u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\u65F6\u4F1A\u81EA\u52A8\u590D\u5236 assets \u548C script \u76EE\u5F55
- \u5982\u679C\u8D44\u6E90\u6587\u4EF6\u672A\u590D\u5236\uFF0C\u8BF7\u4F7F\u7528 diagnose \u64CD\u4F5C\u6392\u67E5\u95EE\u9898`,{action:y.enum(["set-prd","set-output","status","session-info","diagnose"]).describe("\u64CD\u4F5C\u7C7B\u578B"),path:y.string().optional().describe("\u6587\u4EF6\u6216\u76EE\u5F55\u8DEF\u5F84\uFF08set-prd \u548C set-output \u65F6\u5FC5\u9700\uFF09"),workingDirectory:y.string().optional().describe("\u5DE5\u4F5C\u76EE\u5F55\uFF08\u53EF\u9009\uFF0C\u9ED8\u8BA4\u4F7F\u7528\u5F53\u524D\u76EE\u5F55\uFF09")},async({action:n,path:E,workingDirectory:i})=>{try{const a=await M.getSessionManager({workingDirectory:i});switch(n){case"set-prd":return E?(await a.setPrdFilePath(E),{content:[{type:"text",text:`PRD \u6587\u4EF6\u8DEF\u5F84\u5DF2\u8BBE\u7F6E\u4E3A: ${E}`}]}):{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B PRD \u6587\u4EF6\u8DEF\u5F84"}]};case"set-output":if(!E)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B\u8F93\u51FA\u76EE\u5F55\u8DEF\u5F84"}]};try{await a.setOutputDirectory(E),console.log(`\u8F93\u51FA\u76EE\u5F55\u8BBE\u7F6E\u6210\u529F: ${E}`),await F.copyAssetsToTarget(E),console.log(`\u8D44\u6E90\u6587\u4EF6\u590D\u5236\u5B8C\u6210: ${E}`);const p=await import("fs-extra"),$=await import("path"),P=$.join(E,"assets"),B=$.join(E,"script"),v=await p.pathExists(P),w=await p.pathExists(B);let _="";return v&&w?_=`

\u2705 \u9A8C\u8BC1\u6210\u529F\uFF1A
- assets/ \u76EE\u5F55\u5DF2\u521B\u5EFA
- script/ \u76EE\u5F55\u5DF2\u521B\u5EFA
- \u9759\u6001\u8D44\u6E90\u6587\u4EF6\u5DF2\u590D\u5236
- \u542F\u52A8\u811A\u672C\u5DF2\u590D\u5236`:_=`

\u26A0\uFE0F \u9A8C\u8BC1\u8B66\u544A\uFF1A
`+(v?`- assets/ \u76EE\u5F55 \u2705
`:`- assets/ \u76EE\u5F55 \u274C
`)+(w?"- script/ \u76EE\u5F55 \u2705":"- script/ \u76EE\u5F55 \u274C"),{content:[{type:"text",text:`\u2705 \u8F93\u51FA\u76EE\u5F55\u5DF2\u8BBE\u7F6E\u4E3A: ${E}

\u{1F4E6} \u8D44\u6E90\u6587\u4EF6\u5DF2\u81EA\u52A8\u590D\u5236\u5230\u8BE5\u76EE\u5F55${_}

\u{1F4A1} \u60A8\u73B0\u5728\u53EF\u4EE5\uFF1A
1. \u4F7F\u7528"\u751F\u6210\u5165\u53E3\u9875\u9762"\u547D\u4EE4\u521B\u5EFA index.html
2. \u4F7F\u7528"\u751F\u6210\u9875\u9762-[\u6A21\u5757\u540D]"\u547D\u4EE4\u521B\u5EFA\u5177\u4F53\u9875\u9762
3. \u8FDB\u5165\u8F93\u51FA\u76EE\u5F55\uFF0C\u4F7F\u7528 script/ \u4E2D\u7684\u542F\u52A8\u811A\u672C\u9884\u89C8\u9875\u9762`}]}}catch(p){return{isError:!0,content:[{type:"text",text:`\u274C \u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\u5931\u8D25: ${p instanceof Error?p.message:"\u672A\u77E5\u9519\u8BEF"}

\u8BF7\u68C0\u67E5\uFF1A
1. \u76EE\u5F55\u8DEF\u5F84\u662F\u5426\u6B63\u786E
2. \u662F\u5426\u6709\u5199\u5165\u6743\u9650
3. \u78C1\u76D8\u7A7A\u95F4\u662F\u5426\u5145\u8DB3`}]}}case"status":const r=a.getConfigStatus();let m="";if(r.hasOutputDir&&r.outputDirectory)try{const p=await import("fs-extra"),$=await import("path"),P=$.join(r.outputDirectory,"assets"),B=$.join(r.outputDirectory,"script"),v=await p.pathExists(P),w=await p.pathExists(B);m=`

\u{1F4E6} \u8D44\u6E90\u6587\u4EF6\u72B6\u6001:
- Assets \u76EE\u5F55: ${v?"\u2705 \u5DF2\u590D\u5236":"\u274C \u672A\u590D\u5236"}
- Script \u76EE\u5F55: ${w?"\u2705 \u5DF2\u590D\u5236":"\u274C \u672A\u590D\u5236"}`,(!v||!w)&&(m+=`

\u{1F4A1} \u5982\u9700\u91CD\u65B0\u590D\u5236\u8D44\u6E90\u6587\u4EF6\uFF0C\u8BF7\u91CD\u65B0\u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55`)}catch{m=`

\u26A0\uFE0F \u65E0\u6CD5\u68C0\u67E5\u8D44\u6E90\u6587\u4EF6\u72B6\u6001`}return{content:[{type:"text",text:`\u{1F4CB} \u914D\u7F6E\u72B6\u6001\uFF1A
- PRD \u6587\u4EF6: ${r.hasPrdFile?`\u2705 ${r.prdFilePath}`:"\u274C \u672A\u8BBE\u7F6E"}
- \u8F93\u51FA\u76EE\u5F55: ${r.hasOutputDir?`\u2705 ${r.outputDirectory}`:"\u274C \u672A\u8BBE\u7F6E"}
- \u914D\u7F6E\u5B8C\u6574\u6027: ${r.isComplete?"\u2705 \u5B8C\u6574":"\u274C \u4E0D\u5B8C\u6574"}${m}

${r.isComplete?`
\u2705 \u914D\u7F6E\u5B8C\u6574\uFF0C\u53EF\u4EE5\u5F00\u59CB\u751F\u6210\u9875\u9762\uFF01`:`
\u26A0\uFE0F \u8BF7\u5148\u5B8C\u6210\u914D\u7F6E\u8BBE\u7F6E\u540E\u518D\u4F7F\u7528\u9875\u9762\u751F\u6210\u529F\u80FD\u3002`}`}]};case"session-info":const l=a.getSessionInfo(),x=M.detectProjectInfo({workingDirectory:i});return{content:[{type:"text",text:`\u4F1A\u8BDD\u4FE1\u606F\uFF1A
- \u4F1A\u8BDDID: ${a.getSessionId()}
- \u7528\u6237\u6807\u8BC6: ${x.userId}
- \u9879\u76EE\u8DEF\u5F84: ${x.projectPath}
- \u9879\u76EE\u540D\u79F0: ${x.projectName}
- \u521B\u5EFA\u65F6\u95F4: ${new Date(l.createdAt).toLocaleString()}
- \u6700\u540E\u4F7F\u7528: ${new Date(l.lastUsedAt).toLocaleString()}`}]};case"diagnose":try{const p=a.getConfigStatus(),$=await import("fs-extra"),P=await import("path");let B=`\u{1F50D} \u9879\u76EE\u521D\u59CB\u5316\u8BCA\u65AD\u62A5\u544A

`;if(B+=`\u{1F4CB} \u914D\u7F6E\u68C0\u67E5:
`,B+=`- PRD \u6587\u4EF6: ${p.hasPrdFile?"\u2705 \u5DF2\u8BBE\u7F6E":"\u274C \u672A\u8BBE\u7F6E"}
`,B+=`- \u8F93\u51FA\u76EE\u5F55: ${p.hasOutputDir?"\u2705 \u5DF2\u8BBE\u7F6E":"\u274C \u672A\u8BBE\u7F6E"}
`,!p.hasOutputDir)return B+=`
\u274C \u95EE\u9898\u53D1\u73B0: \u8F93\u51FA\u76EE\u5F55\u672A\u8BBE\u7F6E
`,B+=`\u{1F4A1} \u89E3\u51B3\u65B9\u6848: \u8BF7\u63D0\u4F9B\u8F93\u51FA\u76EE\u5F55\u8DEF\u5F84\uFF0C\u6211\u6765\u4E3A\u60A8\u8BBE\u7F6E
`,{content:[{type:"text",text:B}]};const v=p.outputDirectory,w=await $.pathExists(v);if(B+=`- \u8F93\u51FA\u76EE\u5F55\u5B58\u5728: ${w?"\u2705 \u5B58\u5728":"\u274C \u4E0D\u5B58\u5728"}
`,!w)return B+=`
\u274C \u95EE\u9898\u53D1\u73B0: \u8F93\u51FA\u76EE\u5F55\u4E0D\u5B58\u5728
`,B+=`\u{1F4A1} \u89E3\u51B3\u65B9\u6848: \u8BF7\u91CD\u65B0\u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\uFF0C\u7CFB\u7EDF\u4F1A\u81EA\u52A8\u521B\u5EFA
`,{content:[{type:"text",text:B}]};const _=P.join(v,"assets"),I=P.join(v,"script"),A=await $.pathExists(_),s=await $.pathExists(I);if(B+=`
\u{1F4E6} \u8D44\u6E90\u6587\u4EF6\u68C0\u67E5:
`,B+=`- Assets \u76EE\u5F55: ${A?"\u2705 \u5DF2\u590D\u5236":"\u274C \u672A\u590D\u5236"}
`,B+=`- Script \u76EE\u5F55: ${s?"\u2705 \u5DF2\u590D\u5236":"\u274C \u672A\u590D\u5236"}
`,!A||!s)B+=`
\u274C \u95EE\u9898\u53D1\u73B0: \u8D44\u6E90\u6587\u4EF6\u672A\u5B8C\u6574\u590D\u5236
`,B+=`\u{1F4A1} \u89E3\u51B3\u65B9\u6848: \u91CD\u65B0\u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\u5C06\u81EA\u52A8\u590D\u5236\u8D44\u6E90\u6587\u4EF6
`,B+=`
\u{1F527} \u53EF\u80FD\u7684\u539F\u56E0:
`,B+=`1. \u521D\u59CB\u5316\u65F6\u53EA\u8BBE\u7F6E\u4E86PRD\u6587\u4EF6\uFF0C\u672A\u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55
`,B+=`2. \u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\u65F6\u53D1\u751F\u4E86\u9519\u8BEF
`,B+=`3. \u6743\u9650\u95EE\u9898\u5BFC\u81F4\u590D\u5236\u5931\u8D25
`;else{const e=await $.readdir(_),t=await $.readdir(I);B+=`- Assets \u6587\u4EF6\u6570\u91CF: ${e.length}
`,B+=`- Script \u6587\u4EF6\u6570\u91CF: ${t.length}
`,e.length===0||t.length===0?(B+=`
\u26A0\uFE0F \u8B66\u544A: \u8D44\u6E90\u76EE\u5F55\u5B58\u5728\u4F46\u5185\u5BB9\u4E3A\u7A7A
`,B+=`\u{1F4A1} \u89E3\u51B3\u65B9\u6848: \u91CD\u65B0\u8BBE\u7F6E\u8F93\u51FA\u76EE\u5F55\u4EE5\u91CD\u65B0\u590D\u5236\u8D44\u6E90
`):(B+=`
\u2705 \u8BCA\u65AD\u7ED3\u679C: \u9879\u76EE\u521D\u59CB\u5316\u6B63\u5E38
`,B+=`\u{1F389} \u60A8\u53EF\u4EE5\u5F00\u59CB\u751F\u6210\u9875\u9762\u4E86\uFF01
`)}return{content:[{type:"text",text:B}]}}catch(p){return{isError:!0,content:[{type:"text",text:`\u274C \u8BCA\u65AD\u5931\u8D25: ${p instanceof Error?p.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}default:return{isError:!0,content:[{type:"text",text:"\u4E0D\u652F\u6301\u7684\u64CD\u4F5C\u7C7B\u578B"}]}}}catch(a){return{isError:!0,content:[{type:"text",text:`\u64CD\u4F5C\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}}),o.tool("pm-auto-generate",`\u4EA7\u54C1\u7ECF\u7406\u81EA\u52A8\u9875\u9762\u751F\u6210\u5DE5\u5177 - \u81EA\u52A8\u68C0\u6D4B\u7528\u6237\u73AF\u5883\uFF0C\u6839\u636E\u914D\u7F6E\u751F\u6210 HTML \u9875\u9762

\u4F7F\u7528\u65B9\u6CD5\uFF1A
1. \u751F\u6210\u5165\u53E3\u9875\u9762\uFF1A{"action": "start"}
2. \u751F\u6210\u6A21\u5757\u9875\u9762\uFF1A{"action": "page", "module": "\u7528\u6237\u7BA1\u7406"}
3. \u68C0\u67E5\u914D\u7F6E\uFF1A{"action": "check"}
4. \u9A8C\u8BC1\u83DC\u5355\u7ED3\u6784\uFF1A{"action": "validate-menu", "htmlContent": "\u83DC\u5355HTML\u5185\u5BB9"}
5. \u9A8C\u8BC1\u9875\u9762\u7ED3\u6784\uFF1A{"action": "validate-page", "htmlContent": "\u9875\u9762HTML\u5185\u5BB9", "pageType": "list|form|detail"}
6. \u9A8C\u8BC1\u8868\u5355\u7ED3\u6784\uFF1A{"action": "validate-form", "htmlContent": "\u8868\u5355HTML\u5185\u5BB9"}
7. \u9A8C\u8BC1CSS\u517C\u5BB9\u6027\uFF1A{"action": "validate-css", "htmlContent": "\u9875\u9762HTML\u5185\u5BB9", "pageType": "list|form|detail"}

\u6CE8\u610F\uFF1A\u4F1A\u81EA\u52A8\u68C0\u6D4B\u7528\u6237\u73AF\u5883\uFF0C\u65E0\u9700\u624B\u52A8\u6307\u5B9A\u4F1A\u8BDDID`,{action:y.enum(["start","page","check","validate-menu","validate-page","validate-form","validate-css"]).describe("\u751F\u6210\u64CD\u4F5C\u7C7B\u578B"),module:y.string().optional().describe("\u6A21\u5757\u540D\u79F0\uFF08action\u4E3Apage\u65F6\u5FC5\u9700\uFF09"),workingDirectory:y.string().optional().describe("\u5DE5\u4F5C\u76EE\u5F55\uFF08\u53EF\u9009\uFF0C\u9ED8\u8BA4\u4F7F\u7528\u5F53\u524D\u76EE\u5F55\uFF09"),htmlContent:y.string().optional().describe("HTML\u5185\u5BB9\uFF08\u9A8C\u8BC1\u64CD\u4F5C\u65F6\u5FC5\u9700\uFF09"),pageType:y.enum(["list","form","detail"]).optional().describe("\u9875\u9762\u7C7B\u578B\uFF08validate-page/validate-css\u65F6\u5FC5\u9700\uFF09"),basePath:y.string().optional().describe("\u57FA\u7840\u8DEF\u5F84\uFF08validate-page\u65F6\u53EF\u9009\uFF09")},async({action:n,module:E,workingDirectory:i,htmlContent:a,pageType:r,basePath:m})=>{try{const l=await M.getSessionManager({workingDirectory:i});if(!l.isConfigComplete()&&n!=="check")return{isError:!0,content:[{type:"text",text:"\u914D\u7F6E\u4E0D\u5B8C\u6574\uFF0C\u8BF7\u5148\u4F7F\u7528 pm-auto-config \u5DE5\u5177\u8BBE\u7F6E PRD \u6587\u4EF6\u8DEF\u5F84\u548C\u8F93\u51FA\u76EE\u5F55"}]};switch(n){case"check":const x=l.getConfigStatus(),p=M.detectProjectInfo({workingDirectory:i});return{content:[{type:"text",text:`\u914D\u7F6E\u68C0\u67E5\u7ED3\u679C\uFF1A
- \u9879\u76EE: ${p.projectName}
- \u7528\u6237: ${p.userId}
- PRD \u6587\u4EF6: ${x.hasPrdFile?`\u2705 ${x.prdFilePath}`:"\u274C \u672A\u8BBE\u7F6E"}
- \u8F93\u51FA\u76EE\u5F55: ${x.hasOutputDir?`\u2705 ${x.outputDirectory}`:"\u274C \u672A\u8BBE\u7F6E"}
- \u914D\u7F6E\u5B8C\u6574\u6027: ${x.isComplete?"\u2705 \u53EF\u4EE5\u5F00\u59CB\u751F\u6210\u9875\u9762":"\u274C \u8BF7\u5B8C\u6210\u914D\u7F6E\u8BBE\u7F6E"}`}]};case"start":const $=await F.getPromptContent("core/generate-menu.md"),P=await F.getTemplateContent("base-template.html"),B=l.getPrdFilePath(),v=await j.readFile(B,"utf-8");return{content:[{type:"text",text:`\u{1F3AF} \u751F\u6210\u5165\u53E3\u9875\u9762\u4EFB\u52A1

\u8BF7\u4E25\u683C\u6309\u7167\u4EE5\u4E0B\u6B65\u9AA4\u548C\u89C4\u8303\u751F\u6210 index.html \u6587\u4EF6\uFF1A

## \u7B2C\u4E00\u6B65\uFF1A\u89E3\u6790PRD\u83DC\u5355\u7ED3\u6784
\u4ECEPRD\u6587\u4EF6\u7684"\u7B2C\u4E94\u90E8\u5206: \u7CFB\u7EDF\u529F\u80FD\u83DC\u5355\u7ED3\u6784"\u4E2D\u63D0\u53D6\u83DC\u5355\u6570\u636E\uFF0C\u8F6C\u6362\u4E3AJSON\u683C\u5F0F\u3002

## \u7B2C\u4E8C\u6B65\uFF1A\u4E25\u683C\u6309\u7167\u83DC\u5355\u751F\u6210\u89C4\u8303
**\u5FC5\u987B\u4E25\u683C\u9075\u5FAA\u4EE5\u4E0BHTML\u7ED3\u6784\u89C4\u8303**\uFF1A

${$}

## \u7B2C\u4E09\u6B65\uFF1A\u4F7F\u7528\u57FA\u7840\u6A21\u677F
\u57FA\u4E8E\u4EE5\u4E0B\u6A21\u677F\u751F\u6210\u5B8C\u6574\u9875\u9762\uFF1A

<!-- TEMPLATE_START -->
${P}
<!-- TEMPLATE_END -->

## \u7B2C\u56DB\u6B65\uFF1A\u6574\u5408PRD\u5185\u5BB9
\u53C2\u8003PRD\u6587\u4EF6\u5185\u5BB9\uFF1A

<!-- PRD_START -->
${v}
<!-- PRD_END -->

## \u7B2C\u4E94\u6B65\uFF1A\u751F\u6210\u8981\u6C42
1. **\u4E25\u683C\u6309\u7167 generate-menu.md \u4E2D\u7684HTML\u7ED3\u6784\u8981\u6C42**
2. **\u4F7F\u7528\u6B63\u786E\u7684CSS\u7C7B\u540D**\uFF1Afuni-menu-list, funi-menu-item, funi-menu-link \u7B49
3. **\u6B63\u786E\u7684\u56FE\u6807\u683C\u5F0F**\uFF1A<iconify-icon icon="mdi:xxx" class="funi-menu-icon"></iconify-icon>
4. **\u51C6\u786E\u7684\u8DEF\u5F84\u914D\u7F6E**\uFF1A\u4F7F\u7528hash\u8DEF\u7531\u683C\u5F0F
5. **\u5B8C\u6574\u7684\u5D4C\u5957\u7ED3\u6784**\uFF1A\u6709\u5B50\u83DC\u5355\u7684\u4F7F\u7528 funi-menu-group \u7ED3\u6784

\u26A0\uFE0F **\u91CD\u8981**\uFF1A\u4E0D\u8981\u4F7F\u7528\u901A\u7528\u6A21\u677F\uFF0C\u5FC5\u987B\u4E25\u683C\u6309\u7167 generate-menu.md \u4E2D\u5B9A\u4E49\u7684HTML\u7ED3\u6784\u89C4\u8303\u751F\u6210\u83DC\u5355\uFF01

\u751F\u6210\u7684\u6587\u4EF6\u5E94\u4FDD\u5B58\u5230\uFF1A${l.getOutputDirectory()}/index.html`}]};case"page":if(!E)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B\u6A21\u5757\u540D\u79F0"}]};const w=await F.getPromptContent("core/generate-page.md"),_=l.getPrdFilePath(),I=await j.readFile(_,"utf-8");return{content:[{type:"text",text:`\u8BF7\u4F7F\u7528\u4EE5\u4E0B\u63D0\u793A\u8BCD\u751F\u6210 ${E} \u6A21\u5757\u7684\u9875\u9762\uFF1A

=== \u751F\u6210\u6307\u4EE4 ===
\u751F\u6210\u9875\u9762-${E}

=== \u63D0\u793A\u8BCD ===
${w}

=== PRD \u5185\u5BB9 ===
${I}

=== \u751F\u6210\u8BF4\u660E ===
\u8BF7\u6839\u636E\u4E0A\u8FF0\u63D0\u793A\u8BCD\u548C PRD \u5185\u5BB9\uFF0C\u4E3A ${E} \u6A21\u5757\u751F\u6210\u76F8\u5E94\u7684\u9875\u9762\u6587\u4EF6\u3002
\u9875\u9762\u6587\u4EF6\u5E94\u4FDD\u5B58\u5230\uFF1A${l.getOutputDirectory()}/pages/ \u76EE\u5F55\u4E0B`}]};case"validate-menu":if(!a)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B\u8981\u9A8C\u8BC1\u7684HTML\u5185\u5BB9"}]};try{const A=(await import("./pm/menu-validator.mjs")).MenuValidator.validateMenuHTML(a);let s=`\u{1F50D} \u83DC\u5355\u7ED3\u6784\u9A8C\u8BC1\u7ED3\u679C

`;return A.isValid?s+=`\u2705 \u9A8C\u8BC1\u901A\u8FC7\uFF1A\u83DC\u5355\u7ED3\u6784\u7B26\u5408\u89C4\u8303

`:s+=`\u274C \u9A8C\u8BC1\u5931\u8D25\uFF1A\u83DC\u5355\u7ED3\u6784\u4E0D\u7B26\u5408\u89C4\u8303

`,A.errors.length>0&&(s+=`\u{1F6A8} \u9519\u8BEF\uFF1A
`,A.errors.forEach((e,t)=>{s+=`${t+1}. ${e}
`}),s+=`
`),A.warnings.length>0&&(s+=`\u26A0\uFE0F \u8B66\u544A\uFF1A
`,A.warnings.forEach((e,t)=>{s+=`${t+1}. ${e}
`}),s+=`
`),A.suggestions.length>0&&(s+=`\u{1F4A1} \u5EFA\u8BAE\uFF1A
`,A.suggestions.forEach((e,t)=>{s+=`${t+1}. ${e}
`})),{content:[{type:"text",text:s}]}}catch(A){return{isError:!0,content:[{type:"text",text:`\u83DC\u5355\u9A8C\u8BC1\u5931\u8D25: ${A instanceof Error?A.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}case"validate-page":if(!a||!r)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9BHTML\u5185\u5BB9\u548C\u9875\u9762\u7C7B\u578B\uFF08list/form/detail\uFF09"}]};try{const{PageValidator:A}=await import("./pm/page-validator.mjs"),s=await A.validatePageStructure(a,r);let e=`\u{1F50D} \u9875\u9762\u7ED3\u6784\u9A8C\u8BC1\u7ED3\u679C

`;if(s.isValid?(e+=`\u2705 \u9A8C\u8BC1\u901A\u8FC7\uFF1A${r}\u9875\u9762\u7ED3\u6784\u7B26\u5408\u89C4\u8303
`,e+=`\u{1F4CA} \u5408\u89C4\u6027\u8BC4\u5206: ${s.score}/100

`):(e+=`\u274C \u9A8C\u8BC1\u5931\u8D25\uFF1A${r}\u9875\u9762\u7ED3\u6784\u4E0D\u7B26\u5408\u89C4\u8303
`,e+=`\u{1F4CA} \u5408\u89C4\u6027\u8BC4\u5206: ${s.score}/100

`),s.errors.length>0&&(e+=`\u{1F6A8} \u9519\u8BEF\uFF1A
`,s.errors.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`)}),e+=`
`),s.warnings.length>0&&(e+=`\u26A0\uFE0F \u8B66\u544A\uFF1A
`,s.warnings.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`)}),e+=`
`),s.suggestions.length>0&&(e+=`\u{1F4A1} \u4F18\u5316\u5EFA\u8BAE\uFF1A
`,s.suggestions.forEach((t,u)=>{e+=`${u+1}. ${t}
`})),m){const t=A.validatePathConsistency(m,`pages/${m}/${r==="form"?"add-edit":r==="detail"?"detail-review":"list"}.html`,r);t.isValid||(e+=`
\u{1F6E3}\uFE0F \u8DEF\u5F84\u4E00\u81F4\u6027\u68C0\u67E5\uFF1A
`,t.errors.forEach((u,D)=>{e+=`${D+1}. ${u.message}
`,u.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${u.suggestion}
`)}))}return{content:[{type:"text",text:e}]}}catch(A){return{isError:!0,content:[{type:"text",text:`\u9875\u9762\u9A8C\u8BC1\u5931\u8D25: ${A instanceof Error?A.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}case"validate-form":if(!a)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9B\u8981\u9A8C\u8BC1\u7684HTML\u5185\u5BB9"}]};try{const{FormValidator:A}=await import("./pm/page-validator.mjs"),s=A.validateFormStructure(a);let e=`\u{1F50D} \u8868\u5355\u7ED3\u6784\u9A8C\u8BC1\u7ED3\u679C

`;return s.isValid?(e+=`\u2705 \u9A8C\u8BC1\u901A\u8FC7\uFF1A\u8868\u5355\u7ED3\u6784\u7B26\u5408\u89C4\u8303
`,e+=`\u{1F4CA} \u5408\u89C4\u6027\u8BC4\u5206: ${s.score}/100

`):(e+=`\u274C \u9A8C\u8BC1\u5931\u8D25\uFF1A\u8868\u5355\u7ED3\u6784\u4E0D\u7B26\u5408\u89C4\u8303
`,e+=`\u{1F4CA} \u5408\u89C4\u6027\u8BC4\u5206: ${s.score}/100

`),s.errors.length>0&&(e+=`\u{1F6A8} \u9519\u8BEF\uFF1A
`,s.errors.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`)}),e+=`
`),s.warnings.length>0&&(e+=`\u26A0\uFE0F \u8B66\u544A\uFF1A
`,s.warnings.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`)}),e+=`
`),s.suggestions.length>0&&(e+=`\u{1F4A1} \u4F18\u5316\u5EFA\u8BAE\uFF1A
`,s.suggestions.forEach((t,u)=>{e+=`${u+1}. ${t}
`})),{content:[{type:"text",text:e}]}}catch(A){return{isError:!0,content:[{type:"text",text:`\u8868\u5355\u9A8C\u8BC1\u5931\u8D25: ${A instanceof Error?A.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}case"validate-css":if(!a||!r)return{isError:!0,content:[{type:"text",text:"\u8BF7\u63D0\u4F9BHTML\u5185\u5BB9\u548C\u9875\u9762\u7C7B\u578B\uFF08list/form/detail\uFF09"}]};try{const{CSSFrameworkValidator:A}=await import("./pm/css-framework-validator.mjs"),s=await A.validateCSSCompatibility(a,r);let e=`\u{1F3A8} CSS\u6846\u67B6\u517C\u5BB9\u6027\u9A8C\u8BC1\u7ED3\u679C

`;if(s.isValid?(e+=`\u2705 \u9A8C\u8BC1\u901A\u8FC7\uFF1ACSS\u6846\u67B6\u517C\u5BB9\u6027\u826F\u597D
`,e+=`\u{1F4CA} \u517C\u5BB9\u6027\u8BC4\u5206: ${s.score}/100

`):(e+=`\u274C \u9A8C\u8BC1\u5931\u8D25\uFF1A\u5B58\u5728CSS\u6846\u67B6\u517C\u5BB9\u6027\u95EE\u9898
`,e+=`\u{1F4CA} \u517C\u5BB9\u6027\u8BC4\u5206: ${s.score}/100

`),s.errors.length>0&&(e+=`\u{1F6A8} CSS\u517C\u5BB9\u6027\u9519\u8BEF\uFF1A
`,s.errors.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u4FEE\u590D\u5EFA\u8BAE: ${t.suggestion}
`),t.correctClass&&(e+=`   \u{1F527} \u6B63\u786E\u7C7B\u540D: ${t.correctClass}
`)}),e+=`
`),s.missingClasses.length>0&&(e+=`\u{1F50D} \u7F3A\u5931\u7684CSS\u7C7B\u540D\uFF1A
`,s.missingClasses.forEach((t,u)=>{e+=`${u+1}. ${t}
`}),e+=`
`),s.incorrectStructure.length>0&&(e+=`\u{1F3D7}\uFE0F DOM\u7ED3\u6784\u95EE\u9898\uFF1A
`,s.incorrectStructure.forEach((t,u)=>{e+=`${u+1}. ${t.expected}
`,e+=`   \u{1F4CD} \u4F4D\u7F6E: ${t.location}
`,e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`}),e+=`
`),s.warnings.length>0&&(e+=`\u26A0\uFE0F \u8B66\u544A\uFF1A
`,s.warnings.forEach((t,u)=>{e+=`${u+1}. [${t.type}] ${t.message}
`,t.suggestion&&(e+=`   \u{1F4A1} \u5EFA\u8BAE: ${t.suggestion}
`)}),e+=`
`),s.score<80){e+=`\u{1F4CB} \u6B63\u786E\u7684DOM\u7ED3\u6784\u6A21\u677F\uFF1A
`;const t=A.getCorrectDOMStructure(r);e+="```html\n"+t.trim()+"\n```\n\n",e+=`\u{1F527} CSS\u7C7B\u540D\u6620\u5C04\u8868\uFF1A
`;const u=A.getCorrectClassMapping();Object.entries(u).forEach(([D,C])=>{e+=`  ${D} \u2192 ${C}
`})}return{content:[{type:"text",text:e}]}}catch(A){return{isError:!0,content:[{type:"text",text:`CSS\u6846\u67B6\u9A8C\u8BC1\u5931\u8D25: ${A instanceof Error?A.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}default:return{isError:!0,content:[{type:"text",text:"\u4E0D\u652F\u6301\u7684\u751F\u6210\u64CD\u4F5C\u7C7B\u578B"}]}}}catch(l){return{isError:!0,content:[{type:"text",text:`\u9875\u9762\u751F\u6210\u5931\u8D25: ${l instanceof Error?l.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}}),o.tool("pm-prompt",`\u4EA7\u54C1\u7ECF\u7406\u63D0\u793A\u8BCD\u83B7\u53D6\u5DE5\u5177 - \u5185\u90E8\u4F7F\u7528\uFF0C\u83B7\u53D6\u9875\u9762\u751F\u6210\u6240\u9700\u7684\u63D0\u793A\u8BCD\u5185\u5BB9

\u26A0\uFE0F \u6CE8\u610F\uFF1A\u6B64\u5DE5\u5177\u4EC5\u4F9BAI\u667A\u80FD\u4F53\u5185\u90E8\u4F7F\u7528\uFF0C\u4E0D\u5EFA\u8BAE\u7528\u6237\u76F4\u63A5\u8C03\u7528
\u5982\u9700\u4E86\u89E3\u9875\u9762\u751F\u6210\u6D41\u7A0B\uFF0C\u8BF7\u4F7F\u7528 pm-auto-generate \u5DE5\u5177

\u4F7F\u7528\u65B9\u6CD5\uFF1A
1. \u83B7\u53D6\u5F00\u59CB\u63D0\u793A\u8BCD\uFF1A{"type": "start"}
2. \u83B7\u53D6\u9875\u9762\u751F\u6210\u63D0\u793A\u8BCD\uFF1A{"type": "generate-page"}
3. \u83B7\u53D6\u83DC\u5355\u751F\u6210\u63D0\u793A\u8BCD\uFF1A{"type": "generate-menu"}
4. \u83B7\u53D6\u8868\u5355\u751F\u6210\u63D0\u793A\u8BCD\uFF1A{"type": "generate-form"}
5. \u5217\u51FA\u6240\u6709\u63D0\u793A\u8BCD\uFF1A{"type": "list"}
6. \u83B7\u53D6\u81EA\u5B9A\u4E49\u63D0\u793A\u8BCD\uFF1A{"type": "custom", "path": "core/form/generate-select.md"}`,{type:y.enum(["start","generate-page","generate-menu","generate-form","list","custom"]).describe("\u63D0\u793A\u8BCD\u7C7B\u578B"),path:y.string().optional().describe("\u81EA\u5B9A\u4E49\u63D0\u793A\u8BCD\u8DEF\u5F84\uFF08type\u4E3Acustom\u65F6\u5FC5\u9700\uFF09"),userVisible:y.boolean().optional().describe("\u662F\u5426\u5BF9\u7528\u6237\u53EF\u89C1\uFF08\u9ED8\u8BA4false\uFF09")},async({type:n,path:E,userVisible:i=!1})=>{let a="";try{let r="";switch(n){case"start":r=await F.getPromptContent("start.md"),a="\u5165\u53E3\u9875\u9762\u751F\u6210\u63D0\u793A\u8BCD";break;case"generate-page":r=await F.getPromptContent("core/generate-page.md"),a="\u9875\u9762\u751F\u6210\u63D0\u793A\u8BCD";break;case"generate-menu":r=await F.getPromptContent("core/generate-menu.md"),a="\u83DC\u5355\u751F\u6210\u63D0\u793A\u8BCD";break;case"generate-form":r=await F.getPromptContent("core/form/generate-form.md"),a="\u8868\u5355\u751F\u6210\u63D0\u793A\u8BCD";break;case"list":r=`\u53EF\u7528\u7684\u63D0\u793A\u8BCD\u6587\u4EF6\uFF1A

${(await F.listPrompts()).map(m=>`- ${m}`).join(`
`)}`,a="\u63D0\u793A\u8BCD\u5217\u8868";break;case"custom":if(!E)return{isError:!0,content:[{type:"text",text:i?"\u274C \u8BF7\u63D0\u4F9B\u63D0\u793A\u8BCD\u6587\u4EF6\u8DEF\u5F84":"\u8BF7\u63D0\u4F9B\u63D0\u793A\u8BCD\u6587\u4EF6\u8DEF\u5F84"}]};r=await F.getPromptContent(E),a=`\u81EA\u5B9A\u4E49\u63D0\u793A\u8BCD (${E})`;break}return i?{content:[{type:"text",text:`\u2705 ${a}\u83B7\u53D6\u6210\u529F

\u{1F4A1} \u63D0\u793A\uFF1A\u63D0\u793A\u8BCD\u5185\u5BB9\u5DF2\u63D0\u4F9B\u7ED9AI\u667A\u80FD\u4F53\u7528\u4E8E\u9875\u9762\u751F\u6210\uFF0C\u65E0\u9700\u67E5\u770B\u6280\u672F\u7EC6\u8282\u3002
\u5982\u9700\u751F\u6210\u9875\u9762\uFF0C\u8BF7\u4F7F\u7528"\u751F\u6210\u5165\u53E3\u9875\u9762"\u6216"\u751F\u6210\u9875\u9762-[\u6A21\u5757\u540D]"\u547D\u4EE4\u3002`}]}:{content:[{type:"text",text:`<!-- INTERNAL_USE_ONLY -->
${r}`}]}}catch(r){return{isError:!0,content:[{type:"text",text:i?`\u274C \u83B7\u53D6${a||"\u63D0\u793A\u8BCD"}\u5931\u8D25\uFF1A${r instanceof Error?r.message:"\u672A\u77E5\u9519\u8BEF"}`:`\u83B7\u53D6\u63D0\u793A\u8BCD\u5931\u8D25: ${r instanceof Error?r.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}}),o.tool("pm-template",`\u4EA7\u54C1\u7ECF\u7406\u6A21\u677F\u83B7\u53D6\u5DE5\u5177 - \u5185\u90E8\u4F7F\u7528\uFF0C\u83B7\u53D6\u9875\u9762\u751F\u6210\u6240\u9700\u7684\u6A21\u677F\u5185\u5BB9

\u26A0\uFE0F \u6CE8\u610F\uFF1A\u6B64\u5DE5\u5177\u4EC5\u4F9BAI\u667A\u80FD\u4F53\u5185\u90E8\u4F7F\u7528\uFF0C\u4E0D\u5EFA\u8BAE\u7528\u6237\u76F4\u63A5\u8C03\u7528
\u5982\u9700\u4E86\u89E3\u9875\u9762\u7ED3\u6784\uFF0C\u8BF7\u4F7F\u7528 pm-auto-generate \u5DE5\u5177\u751F\u6210\u5B9E\u9645\u9875\u9762

\u4F7F\u7528\u65B9\u6CD5\uFF1A
1. \u83B7\u53D6\u57FA\u7840\u6A21\u677F\uFF1A{"type": "base"}
2. \u83B7\u53D6\u5217\u8868\u9875\u6A21\u677F\uFF1A{"type": "list"}
3. \u83B7\u53D6\u8868\u5355\u9875\u6A21\u677F\uFF1A{"type": "form"}
4. \u83B7\u53D6\u8BE6\u60C5\u9875\u6A21\u677F\uFF1A{"type": "detail"}
5. \u5217\u51FA\u6240\u6709\u6A21\u677F\uFF1A{"type": "list-all"}
6. \u83B7\u53D6\u81EA\u5B9A\u4E49\u6A21\u677F\uFF1A{"type": "custom", "path": "templates/custom-template.html"}`,{type:y.enum(["base","list","form","detail","list-all","custom"]).describe("\u6A21\u677F\u7C7B\u578B"),path:y.string().optional().describe("\u81EA\u5B9A\u4E49\u6A21\u677F\u8DEF\u5F84\uFF08type\u4E3Acustom\u65F6\u5FC5\u9700\uFF09"),userVisible:y.boolean().optional().describe("\u662F\u5426\u5BF9\u7528\u6237\u53EF\u89C1\uFF08\u9ED8\u8BA4false\uFF09")},async({type:n,path:E,userVisible:i=!1})=>{let a="";try{let r="";switch(n){case"base":r=await F.getTemplateContent("base-template.html"),a="\u57FA\u7840\u9875\u9762\u6A21\u677F";break;case"list":r=await F.getTemplateContent("templates/list-page-template.html"),a="\u5217\u8868\u9875\u9762\u6A21\u677F";break;case"form":r=await F.getTemplateContent("templates/form-page-template.html"),a="\u8868\u5355\u9875\u9762\u6A21\u677F";break;case"detail":r=await F.getTemplateContent("templates/detail-page-template.html"),a="\u8BE6\u60C5\u9875\u9762\u6A21\u677F";break;case"list-all":r=`\u53EF\u7528\u7684\u6A21\u677F\u6587\u4EF6\uFF1A

${(await F.listTemplates()).map(m=>`- ${m}`).join(`
`)}`,a="\u6A21\u677F\u5217\u8868";break;case"custom":if(!E)return{isError:!0,content:[{type:"text",text:i?"\u274C \u8BF7\u63D0\u4F9B\u6A21\u677F\u6587\u4EF6\u8DEF\u5F84":"\u8BF7\u63D0\u4F9B\u6A21\u677F\u6587\u4EF6\u8DEF\u5F84"}]};r=await F.getTemplateContent(E),a=`\u81EA\u5B9A\u4E49\u6A21\u677F (${E})`;break}return i?{content:[{type:"text",text:`\u2705 ${a}\u83B7\u53D6\u6210\u529F

\u{1F4A1} \u63D0\u793A\uFF1A\u6A21\u677F\u5185\u5BB9\u5DF2\u63D0\u4F9B\u7ED9AI\u667A\u80FD\u4F53\u7528\u4E8E\u9875\u9762\u751F\u6210\uFF0C\u65E0\u9700\u67E5\u770B\u6280\u672F\u7EC6\u8282\u3002
\u5982\u9700\u751F\u6210\u9875\u9762\uFF0C\u8BF7\u4F7F\u7528"\u751F\u6210\u5165\u53E3\u9875\u9762"\u6216"\u751F\u6210\u9875\u9762-[\u6A21\u5757\u540D]"\u547D\u4EE4\u3002`}]}:{content:[{type:"text",text:`<!-- INTERNAL_USE_ONLY -->
${r}`}]}}catch(r){return{isError:!0,content:[{type:"text",text:i?`\u274C \u83B7\u53D6${a||"\u6A21\u677F"}\u5931\u8D25\uFF1A${r instanceof Error?r.message:"\u672A\u77E5\u9519\u8BEF"}`:`\u83B7\u53D6\u6A21\u677F\u5931\u8D25: ${r instanceof Error?r.message:"\u672A\u77E5\u9519\u8BEF"}`}]}}})}const Vu=ou({meta:{name:"mcp-instruct",version:L,description:"Run the MCP starter with stdio, http, or sse transport"},args:{http:{type:"boolean",description:"Run with HTTP transport"},sse:{type:"boolean",description:"Run with SSE transport"},stdio:{type:"boolean",description:"Run with stdio transport (default)"},port:{type:"string",description:"Port for http/sse (default 3000)",default:"3000"},endpoint:{type:"string",description:"HTTP endpoint (default /mcp)",default:"/mcp"}},async run({args:o}){const F=o.http?"http":o.sse?"sse":"stdio",n=$u({name:"FuniA0Mcp",version:L});process.on("SIGTERM",()=>U(n)),process.on("SIGINT",()=>U(n)),Nu({mcp:n}),Ru({mcp:n}),Mu({mcp:n}),F==="http"?await V(n,{type:"http",port:Number(o.port),endpoint:o.endpoint}):F==="sse"?(console.log("Starting SSE server..."),await V(n,{type:"sse",port:Number(o.port)})):F==="stdio"&&await V(n,{type:"stdio"})}}),Ou=()=>su(Vu);export{Ou as runMain};
