class o{static validateMenuHTML(u){const e={isValid:!0,errors:[],warnings:[],suggestions:[]};return this.checkBasicStructure(u,e),this.checkCSSClasses(u,e),this.checkIconFormat(u,e),this.checkPathFormat(u,e),this.checkNestingStructure(u,e),e.isValid=e.errors.length===0,e}static extractMenuFromPRD(u){const e=this.findMenuSection(u);return e?this.parseMenuStructure(e):[]}static generateMenuHTML(u){let e=`<nav class="funi-menu">
`;for(const n of u)n.children&&n.children.length>0?e+=this.generateMenuGroup(n):e+=this.generateMenuItem(n);return e+="</nav>",e}static checkBasicStructure(u,e){u.includes('class="funi-menu"')||e.errors.push('\u7F3A\u5C11 <nav class="funi-menu"> \u5BB9\u5668'),u.includes('class="funi-menu-list"')||e.errors.push('\u7F3A\u5C11 <ul class="funi-menu-list"> \u83DC\u5355\u5217\u8868'),u.includes('class="funi-menu-item"')||e.errors.push('\u7F3A\u5C11 <li class="funi-menu-item"> \u83DC\u5355\u9879')}static checkCSSClasses(u,e){const n=["funi-menu","funi-menu-list","funi-menu-item","funi-menu-link","funi-menu-text"];for(const i of n)u.includes(`class="${i}"`)||e.errors.push(`\u7F3A\u5C11\u5FC5\u9700\u7684CSS\u7C7B: ${i}`);if(u.includes("children")||u.includes("\u5B50\u83DC\u5355")){const i=["funi-menu-group","funi-menu-group-title","funi-menu-group-toggle"];for(const t of i)u.includes(`class="${t}"`)||e.warnings.push(`\u53EF\u80FD\u7F3A\u5C11\u83DC\u5355\u7EC4CSS\u7C7B: ${t}`)}}static checkIconFormat(u,e){const n=/<iconify-icon\s+icon="[^"]+"\s+class="funi-menu-icon"><\/iconify-icon>/g;u.match(n)||e.warnings.push('\u672A\u627E\u5230\u7B26\u5408\u89C4\u8303\u7684\u56FE\u6807\u683C\u5F0F\uFF0C\u5E94\u4F7F\u7528 <iconify-icon icon="mdi:xxx" class="funi-menu-icon"></iconify-icon>'),(u.includes("<i class=")||u.includes('<span class="icon'))&&e.errors.push("\u4F7F\u7528\u4E86\u9519\u8BEF\u7684\u56FE\u6807\u683C\u5F0F\uFF0C\u5E94\u4F7F\u7528 iconify-icon \u6807\u7B7E")}static checkPathFormat(u,e){const n=/href="([^"]+)"/g,i=u.matchAll(n);for(const t of i){const s=t[1];!s.startsWith("#/")&&!s.startsWith("/")&&e.warnings.push(`\u8DEF\u5F84\u683C\u5F0F\u53EF\u80FD\u4E0D\u6B63\u786E: ${s}\uFF0C\u5EFA\u8BAE\u4F7F\u7528hash\u8DEF\u7531\u683C\u5F0F #/path`)}}static checkNestingStructure(u,e){u.includes("funi-menu-group")&&(u.includes("funi-menu-group-title")||e.errors.push("\u83DC\u5355\u7EC4\u7F3A\u5C11 funi-menu-group-title"),u.includes("funi-menu-group-toggle")||e.warnings.push("\u83DC\u5355\u7EC4\u7F3A\u5C11\u5C55\u5F00/\u6298\u53E0\u56FE\u6807 funi-menu-group-toggle"))}static findMenuSection(u){const e=[/第[一二三四五六七八九十\d]+部分[：:]\s*系统功能菜单结构([\s\S]*?)(?=第[一二三四五六七八九十\d]+部分|$)/i,/系统功能菜单结构[：:]?([\s\S]*?)(?=第[一二三四五六七八九十\d]+部分|$)/i,/菜单结构[：:]?([\s\S]*?)(?=第[一二三四五六七八九十\d]+部分|$)/i,/功能菜单[：:]?([\s\S]*?)(?=第[一二三四五六七八九十\d]+部分|$)/i];for(const n of e){const i=u.match(n);if(i)return i[1]?i[1].trim():i[0].trim()}return null}static parseMenuStructure(u){const e=u.split(`
`).filter(t=>t.trim()),n=[];let i=null;for(const t of e){const s=t.trim();if(!s||s.startsWith("#")||s.startsWith("\u7B2C"))continue;const a=t.length-t.trimStart().length;if(s.startsWith("-")||s.startsWith("*")){const c=s.substring(1).trim();if(a<=2){const r={title:c,icon:this.generateIcon(c),children:[]};this.hasChildren(e,e.indexOf(t))||(r.path=this.generatePath(c),delete r.children),n.push(r),i=r}else i&&i.children&&i.children.push({title:c,path:this.generatePath(c,i.title)})}}return n}static hasChildren(u,e){if(e>=u.length-1)return!1;const n=u[e].length-u[e].trimStart().length,i=u[e+1];return i.length-i.trimStart().length>n&&(i.trim().startsWith("-")||i.trim().startsWith("*"))}static generateIcon(u){const e={\u5DE5\u4F5C\u53F0:"mdi:view-dashboard",\u4EEA\u8868\u677F:"mdi:view-dashboard",\u9996\u9875:"mdi:home",\u7528\u6237\u7BA1\u7406:"mdi:account-group",\u7528\u6237:"mdi:account",\u89D2\u8272\u7BA1\u7406:"mdi:account-key",\u6743\u9650\u7BA1\u7406:"mdi:shield-account",\u7CFB\u7EDF\u8BBE\u7F6E:"mdi:cog",\u8BBE\u7F6E:"mdi:cog",\u91C7\u8D2D:"mdi:cart",\u8BA2\u5355:"mdi:file-document",\u9879\u76EE:"mdi:folder-open",\u6587\u6863:"mdi:file-document-outline",\u62A5\u8868:"mdi:chart-line",\u7EDF\u8BA1:"mdi:chart-bar",\u65E5\u5FD7:"mdi:text-box",\u76D1\u63A7:"mdi:monitor",\u901A\u77E5:"mdi:bell",\u6D88\u606F:"mdi:message"};for(const[n,i]of Object.entries(e))if(u.includes(n))return i;return"mdi:circle-medium"}static generatePath(u,e){const n={\u5DE5\u4F5C\u53F0:"/dashboard",\u4EEA\u8868\u677F:"/dashboard",\u9996\u9875:"/home",\u7528\u6237\u7BA1\u7406:"/user-management",\u89D2\u8272\u7BA1\u7406:"/role-management",\u6743\u9650\u7BA1\u7406:"/permission-management",\u7CFB\u7EDF\u8BBE\u7F6E:"/system-settings"};if(n[u])return n[u];let i=u.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"").toLowerCase();return e?i=`/${this.generatePath(e).replace("/","")}/${i}`:i=`/${i}`,i}static generateMenuItem(u){return`  <ul class="funi-menu-list">
    <li class="funi-menu-item">
      <a href="${u.path}" class="funi-menu-link">
        <iconify-icon icon="${u.icon}" class="funi-menu-icon"></iconify-icon>
        <span class="funi-menu-text">${u.title}</span>
      </a>
    </li>
  </ul>
`}static generateMenuGroup(u){let e=`  <div class="funi-menu-group" data-group-id="${u.title.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g,"-").toLowerCase()}">
    <div class="funi-menu-group-title">
      <div style="display: flex; align-items: center;">
        <iconify-icon icon="${u.icon}" class="funi-menu-icon"></iconify-icon>
        <span>${u.title}</span>
      </div>
      <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
      </svg>
    </div>
    <ul class="funi-menu-list">
`;if(u.children)for(const n of u.children)e+=`      <li class="funi-menu-item">
        <a href="${n.path}" class="funi-menu-link">
          <span class="funi-menu-text">${n.title}</span>
        </a>
      </li>
`;return e+=`    </ul>
  </div>
`,e}}export{o as MenuValidator};
