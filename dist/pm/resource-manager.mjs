import r from"fs-extra";import*as u from"path";import{fileURLToPath as E}from"url";const n=E(import.meta.url),o=u.dirname(n);class l{assetsPath;toolsPath;scriptPath;constructor(){if(n.includes("dist")){const t=u.dirname(o);this.assetsPath=u.join(t,"pm","assets"),this.toolsPath=u.join(t,"pm","tools"),this.scriptPath=u.join(t,"pm","tools","script")}else this.assetsPath=u.join(o,"assets"),this.toolsPath=u.join(o,"tools"),this.scriptPath=u.join(o,"tools","script")}async copyAssetsToTarget(t){try{await r.ensureDir(t);const s=u.join(t,"assets");await r.ensureDir(s),await r.copy(this.assetsPath,s,{overwrite:!0}),console.log(`\u9759\u6001\u8D44\u6E90\u6587\u4EF6\u5DF2\u6210\u529F\u590D\u5236\u5230: ${s}`);const a=u.join(t,"script");await r.ensureDir(a),await r.copy(this.scriptPath,a,{overwrite:!0}),console.log(`\u542F\u52A8\u811A\u672C\u6587\u4EF6\u5DF2\u6210\u529F\u590D\u5236\u5230: ${a}`)}catch(s){throw new Error(`\u590D\u5236\u8D44\u6E90\u6587\u4EF6\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async getPromptContent(t){try{const s=u.join(this.toolsPath,"prompts",t);if(!await r.pathExists(s))throw new Error(`\u63D0\u793A\u8BCD\u6587\u4EF6\u4E0D\u5B58\u5728: ${t}`);return await r.readFile(s,"utf-8")}catch(s){throw new Error(`\u8BFB\u53D6\u63D0\u793A\u8BCD\u6587\u4EF6\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async getTemplateContent(t){try{const s=u.join(this.toolsPath,"framework",t);if(!await r.pathExists(s))throw new Error(`\u6A21\u677F\u6587\u4EF6\u4E0D\u5B58\u5728: ${t}`);return await r.readFile(s,"utf-8")}catch(s){throw new Error(`\u8BFB\u53D6\u6A21\u677F\u6587\u4EF6\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async listPrompts(){try{const t=u.join(this.toolsPath,"prompts");return(await this.getFilesRecursively(t,".md")).map(s=>u.relative(t,s))}catch(t){throw new Error(`\u5217\u51FA\u63D0\u793A\u8BCD\u6587\u4EF6\u5931\u8D25: ${t instanceof Error?t.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async listTemplates(){try{const t=u.join(this.toolsPath,"framework");return(await this.getFilesRecursively(t,".html")).map(s=>u.relative(t,s))}catch(t){throw new Error(`\u5217\u51FA\u6A21\u677F\u6587\u4EF6\u5931\u8D25: ${t instanceof Error?t.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async getFilesRecursively(t,s){const a=[];if(!await r.pathExists(t))return a;const c=await r.readdir(t);for(const i of c){const e=u.join(t,i);if((await r.stat(e)).isDirectory()){const h=await this.getFilesRecursively(e,s);a.push(...h)}else u.extname(i)===s&&a.push(e)}return a}async checkAssetsExist(){return await r.pathExists(this.assetsPath)}async checkToolsExist(){return await r.pathExists(this.toolsPath)}async checkScriptExist(){return await r.pathExists(this.scriptPath)}}export{l as ResourceManager};
