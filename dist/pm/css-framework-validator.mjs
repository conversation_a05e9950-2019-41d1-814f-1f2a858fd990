import*as n from"path";class c{static cssClassDefinitions=new Map;static isInitialized=!1;static CORRECT_CLASS_MAPPINGS={"button text":"funi-btn funi-btn-text","button primary":"funi-btn funi-btn-primary","button secondary":"funi-btn funi-btn-default",button:"funi-btn funi-btn-default",btn:"funi-btn","btn-primary":"funi-btn funi-btn-primary","btn-text":"funi-btn funi-btn-text","search-container":"search-area","search-item":"search-form-item","action-container":"action-buttons","btn-container":"action-buttons","content-container":"container","main-container":"container","header-container":"container-header","table-wrapper":"container-table",table:"data-table","funi-table":"data-table"};static async initialize(t){if(this.isInitialized)return;const e=t||n.join(process.cwd(),"dist/pm/assets/css");try{const i=await import("fs-extra"),u=["funi-framework.css","funi-components.css","funi-themes.css","funi-list.css","funi-form.css"];for(const a of u){const s=n.join(e,a);if(await i.pathExists(s)){const r=await i.readFile(s,"utf-8");this.extractCSSClasses(r)}}this.isInitialized=!0}catch(i){console.warn("CSS\u6846\u67B6\u9A8C\u8BC1\u5668\u521D\u59CB\u5316\u5931\u8D25:",i),this.initializeWithPredefinedClasses()}}static async validateCSSCompatibility(t,e){await this.initialize();const i={isValid:!0,errors:[],warnings:[],suggestions:[],score:100,missingClasses:[],incorrectStructure:[]};return this.checkClassNameCompatibility(t,i),this.checkButtonStyleCompliance(t,i),this.checkDOMStructure(t,e,i),i.score=this.calculateCompatibilityScore(i),i.isValid=i.score>=80&&i.errors.length===0,i}static checkClassNameCompatibility(t,e){[{pattern:/class="button text"/g,correct:"funi-btn funi-btn-text"},{pattern:/class="button primary"/g,correct:"funi-btn funi-btn-primary"},{pattern:/class="button"/g,correct:"funi-btn funi-btn-default"}].forEach(({pattern:i,correct:u})=>{const a=t.match(i);a&&e.errors.push({type:"deprecated-class",message:`\u53D1\u73B0\u8FC7\u65F6\u7684\u6309\u94AE\u7C7B\u540D: ${a[0]}`,correctClass:u,suggestion:`\u8BF7\u4F7F\u7528\u6846\u67B6\u6807\u51C6\u7C7B\u540D: class="${u}"`})})}static checkButtonStyleCompliance(t,e){const i=/<td[^>]*>[\s\S]*?<button[^>]*onclick="[^"]*Action[^"]*"[^>]*>[\s\S]*?<\/td>/g,u=t.match(i);u&&u.forEach(a=>{a.includes("funi-btn-text")||e.errors.push({type:"incorrect-structure",message:"\u64CD\u4F5C\u5217\u6309\u94AE\u5FC5\u987B\u4F7F\u7528\u6587\u672C\u6837\u5F0F",suggestion:"\u8BF7\u4E3A\u64CD\u4F5C\u5217\u6309\u94AE\u6DFB\u52A0 funi-btn-text \u7C7B\u540D",location:"\u8868\u683C\u64CD\u4F5C\u5217"})})}static checkDOMStructure(t,e,i){this.getRequiredStructures(e).forEach(u=>{t.includes(u.pattern)||i.incorrectStructure.push({expected:u.description,actual:"\u7F3A\u5931",location:u.location,suggestion:u.suggestion})})}static getRequiredStructures(t){return{list:[{pattern:'class="container"',description:"\u4E3B\u5BB9\u5668\u5FC5\u987B\u4F7F\u7528 container \u7C7B",location:"\u9875\u9762\u6839\u5143\u7D20",suggestion:'\u786E\u4FDD\u9875\u9762\u6839\u5143\u7D20\u4F7F\u7528 <div id="app" class="container">'},{pattern:'class="action-buttons"',description:"\u64CD\u4F5C\u6309\u94AE\u533A\u57DF\u5FC5\u987B\u4F7F\u7528 action-buttons \u7C7B",location:"\u64CD\u4F5C\u6309\u94AE\u5BB9\u5668",suggestion:'\u4F7F\u7528 <div class="action-buttons"> \u5305\u88C5\u64CD\u4F5C\u6309\u94AE'}],form:[{pattern:'class="form-container"',description:"\u8868\u5355\u5BB9\u5668\u5FC5\u987B\u4F7F\u7528 form-container \u7C7B",location:"\u8868\u5355\u6839\u5BB9\u5668",suggestion:'\u4F7F\u7528 <div class="form-container"> \u5305\u88C5\u8868\u5355\u5185\u5BB9'}],detail:[{pattern:'class="detail-container"',description:"\u8BE6\u60C5\u5BB9\u5668\u5FC5\u987B\u4F7F\u7528 detail-container \u7C7B",location:"\u8BE6\u60C5\u9875\u6839\u5BB9\u5668",suggestion:'\u4F7F\u7528 <div class="detail-container"> \u5305\u88C5\u8BE6\u60C5\u5185\u5BB9'}]}[t]||[]}static calculateCompatibilityScore(t){let e=100;return e-=t.errors.length*20,e-=t.warnings.length*5,e-=t.missingClasses.length*10,e-=t.incorrectStructure.length*15,Math.max(0,e)}static getCorrectClassMapping(){return this.CORRECT_CLASS_MAPPINGS}static getCorrectDOMStructure(t){const e={list:`<div id="app" class="container">
  <div class="container-header">
    <div class="search-area collapsed">
      <form class="search-form">
        <div class="search-form-item">
          <label>\u6807\u7B7E:</label>
          <input type="text" />
        </div>
        <div class="search-form-item search-buttons-item">
          <button class="funi-btn funi-btn-primary">\u67E5\u8BE2</button>
          <button class="funi-btn funi-btn-default">\u91CD\u7F6E</button>
        </div>
      </form>
    </div>
  </div>
  <div class="container-table">
    <div class="action-buttons">
      <button class="funi-btn funi-btn-primary">\u65B0\u5EFA</button>
    </div>
    <div class="table-container">
      <table class="data-table">
        <thead>...</thead>
        <tbody>...</tbody>
      </table>
    </div>
  </div>
</div>`,form:`<div id="app" class="container">
  <div class="form-container">
    <div class="form-section-title">\u8868\u5355\u6807\u9898</div>
    <form class="form-grid">
      <div class="form-item-row">
        <label class="form-item-label">\u6807\u7B7E:</label>
        <div class="form-item-value">
          <input type="text" />
        </div>
      </div>
    </form>
  </div>
</div>`,detail:`<div id="app" class="container">
  <div class="detail-container">
    <div class="detail-section-title">\u8BE6\u60C5\u6807\u9898</div>
    <div class="detail-grid">
      <div class="detail-item-row">
        <label class="detail-item-label">\u6807\u7B7E:</label>
        <div class="detail-item-value">\u503C</div>
      </div>
    </div>
  </div>
</div>`};return e[t]||e.list}static initializeWithPredefinedClasses(){const t=["container","container-header","container-table","search-area","search-form","search-form-item","search-buttons-item","collapsed","date-range-picker","action-buttons","funi-btn","funi-btn-primary","funi-btn-text","funi-btn-default","table-container","data-table","pagination-container","form-container","form-section-title","form-grid","form-item-row","form-item-label","form-item-value","input-and-tip-wrapper","form-item-tip","required","detail-grid","detail-section-title","detail-item","funi-menu","funi-menu-list","funi-menu-item","funi-menu-link","funi-menu-text","funi-menu-icon","funi-menu-group","funi-menu-group-title","funi-menu-group-toggle","tabs","tab-item","active","table-link"];for(const e of t)this.cssClassDefinitions.set(e,!0);this.isInitialized=!0}static async validateCSSCompatibility(t,e){await this.initialize();const i={isValid:!0,errors:[],warnings:[],suggestions:[],score:100,missingClasses:[],incorrectStructure:[]},u=this.extractHTMLClasses(t);return this.validateClassExistence(u,i),this.validateDOMStructure(t,e,i),this.validatePageSpecificStructure(t,e,i),i.score=this.calculateCompatibilityScore(i),i.isValid=i.errors.length===0&&i.score>=80,i}static getCorrectClassMapping(){return{"search-container":"search-area","search-item":"search-form-item","search-input":"search-form-item input","search-select":"search-form-item select","action-container":"action-buttons","action-button":"button","btn-container":"action-buttons","content-container":"container","main-container":"container","page-container":"container","header-container":"container-header","table-wrapper":"container-table","table-container":"table-container","data-grid":"data-table","list-table":"data-table","form-container":"form-container","form-item":"form-item-row","form-label":"form-item-label","form-input":"form-item-value",pagination:"pagination-container","page-nav":"pagination-container"}}static getCorrectDOMStructure(t){return{list:`
<div id="app" class="container">
  <div class="container-header">
    <div class="search-area collapsed">
      <form class="search-form">
        <div class="search-form-item">
          <label>\u6807\u7B7E:</label>
          <input type="text" />
        </div>
        <div class="search-form-item search-buttons-item">
          <button class="button primary">\u67E5\u8BE2</button>
          <button class="button">\u91CD\u7F6E</button>
        </div>
      </form>
    </div>
  </div>
  <div class="container-table">
    <div class="action-buttons">
      <button class="button primary">\u65B0\u5EFA</button>
    </div>
    <div class="table-container">
      <table class="data-table">
        <thead>...</thead>
        <tbody>...</tbody>
      </table>
    </div>
  </div>
</div>`,form:`
<div id="app" class="form-container">
  <div class="form-section-title">\u6807\u9898</div>
  <form class="form-grid">
    <div class="form-item-row">
      <label class="form-item-label">\u6807\u7B7E:</label>
      <div class="form-item-value">
        <div class="input-and-tip-wrapper">
          <input type="text" />
        </div>
      </div>
    </div>
  </form>
</div>`,detail:`
<div id="app" class="container">
  <div class="detail-section-title">\u6807\u9898</div>
  <div class="detail-grid">
    <div class="detail-item">
      <label>\u6807\u7B7E:</label>
      <span>\u503C</span>
    </div>
  </div>
</div>`}[t]}static extractCSSClasses(t){const e=/\.([a-zA-Z][a-zA-Z0-9_-]*)/g;let i;for(;(i=e.exec(t))!==null;){const u=i[1];!u.includes(":")&&!u.includes("@")&&this.cssClassDefinitions.set(u,!0)}}static extractHTMLClasses(t){const e=/class=["']([^"']+)["']/g,i=[];let u;for(;(u=e.exec(t))!==null;){const a=u[1].split(/\s+/).filter(s=>s.trim());i.push(...a)}return[...new Set(i)]}static validateClassExistence(t,e){const i=this.getCorrectClassMapping();for(const u of t)if(!this.cssClassDefinitions.has(u)){const a=i[u];a?e.errors.push({type:"missing-class",message:`\u4F7F\u7528\u4E86\u4E0D\u5B58\u5728\u7684CSS\u7C7B: ${u}`,suggestion:`\u5E94\u8BE5\u4F7F\u7528: ${a}`,correctClass:a}):e.errors.push({type:"missing-class",message:`CSS\u7C7B\u4E0D\u5B58\u5728: ${u}`,suggestion:"\u8BF7\u68C0\u67E5CSS\u6846\u67B6\u6587\u6863\uFF0C\u4F7F\u7528\u6B63\u786E\u7684\u7C7B\u540D"}),e.missingClasses.push(u)}}static validateDOMStructure(t,e,i){e==="list"?this.validateListPageStructure(t,i):e==="form"?this.validateFormPageStructure(t,i):e==="detail"&&this.validateDetailPageStructure(t,i)}static validateListPageStructure(t,e){const i=[{pattern:/<div[^>]*class="[^"]*container[^"]*"[^>]*>[\s\S]*<div[^>]*class="[^"]*container-header[^"]*"[^>]*>/,message:"\u7F3A\u5C11\u6B63\u786E\u7684\u9875\u9762\u5BB9\u5668\u7ED3\u6784",suggestion:'\u4F7F\u7528 <div class="container"> \u5305\u542B <div class="container-header">'},{pattern:/<div[^>]*class="[^"]*search-area[^"]*"[^>]*>/,message:"\u641C\u7D22\u533A\u57DF\u5E94\u4F7F\u7528 search-area \u7C7B\u540D",suggestion:"\u5C06 search-container \u6539\u4E3A search-area"},{pattern:/<div[^>]*class="[^"]*search-form-item[^"]*"[^>]*>/,message:"\u641C\u7D22\u9879\u5E94\u4F7F\u7528 search-form-item \u7C7B\u540D",suggestion:"\u5C06 search-item \u6539\u4E3A search-form-item"},{pattern:/<div[^>]*class="[^"]*action-buttons[^"]*"[^>]*>/,message:"\u64CD\u4F5C\u6309\u94AE\u533A\u57DF\u5E94\u4F7F\u7528 action-buttons \u7C7B\u540D",suggestion:"\u5C06 action-container \u6539\u4E3A action-buttons"},{pattern:/<div[^>]*class="[^"]*container-table[^"]*"[^>]*>/,message:"\u8868\u683C\u533A\u57DF\u5E94\u5305\u88C5\u5728 container-table \u4E2D",suggestion:'\u5C06\u64CD\u4F5C\u6309\u94AE\u548C\u8868\u683C\u5305\u88C5\u5728 <div class="container-table"> \u4E2D'}];for(const u of i)u.pattern.test(t)||(e.incorrectStructure.push({expected:u.suggestion,actual:"\u5F53\u524D\u7ED3\u6784\u4E0D\u7B26\u5408\u8981\u6C42",location:"\u9875\u9762\u7ED3\u6784",suggestion:u.suggestion}),e.errors.push({type:"incorrect-structure",message:u.message,suggestion:u.suggestion}))}static validateFormPageStructure(t,e){t.includes("form-container")||e.errors.push({type:"incorrect-structure",message:"\u8868\u5355\u9875\u9762\u7F3A\u5C11 form-container \u5BB9\u5668",suggestion:'\u4F7F\u7528 <div class="form-container"> \u4F5C\u4E3A\u8868\u5355\u5916\u5C42\u5BB9\u5668'}),t.includes("form-grid")||e.errors.push({type:"incorrect-structure",message:"\u8868\u5355\u7F3A\u5C11 form-grid \u5E03\u5C40\u7C7B",suggestion:'\u4F7F\u7528 <form class="form-grid"> \u5B9E\u73B0\u6807\u51C6\u5E03\u5C40'})}static validateDetailPageStructure(t,e){t.includes("detail-grid")||e.warnings.push({type:"layout-issue",message:"\u8BE6\u60C5\u9875\u9762\u5EFA\u8BAE\u4F7F\u7528 detail-grid \u5E03\u5C40",suggestion:'\u4F7F\u7528 <div class="detail-grid"> \u5B9E\u73B0\u8BE6\u60C5\u9875\u9762\u5E03\u5C40'})}static validatePageSpecificStructure(t,e,i){e==="list"&&(t.includes("search-area")&&!t.includes("container-header")&&i.warnings.push({type:"layout-issue",message:"search-area \u5E94\u8BE5\u5D4C\u5957\u5728 container-header \u4E2D",suggestion:'\u5C06\u641C\u7D22\u533A\u57DF\u653E\u5728 <div class="container-header"> \u5185'}),t.includes("action-buttons")&&!t.includes("container-table")&&i.warnings.push({type:"layout-issue",message:"action-buttons \u5E94\u8BE5\u5728 container-table \u5185",suggestion:'\u5C06\u64CD\u4F5C\u6309\u94AE\u653E\u5728 <div class="container-table"> \u5185'}))}static calculateCompatibilityScore(t){let e=100;return e-=t.errors.length*20,e-=t.warnings.length*10,e-=t.missingClasses.length*15,Math.max(0,e)}}export{c as CSSFrameworkValidator};
