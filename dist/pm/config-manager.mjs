import e from"fs-extra";import*as s from"path";import*as r from"os";class o{configPath;config;constructor(){const t=s.join(r.homedir(),".funi-mcp");this.configPath=s.join(t,"pm-config.json"),this.config={}}async loadConfig(){try{if(await e.pathExists(this.configPath)){const t=await e.readFile(this.configPath,"utf-8");this.config=JSON.parse(t)}else this.config={userPreferences:{language:"zh-CN",theme:"light"}},await this.saveConfig();return this.config}catch(t){throw new Error(`\u52A0\u8F7D\u914D\u7F6E\u6587\u4EF6\u5931\u8D25: ${t instanceof Error?t.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async saveConfig(){try{await e.ensureDir(s.dirname(this.configPath)),await e.writeFile(this.configPath,JSON.stringify(this.config,null,2),"utf-8")}catch(t){throw new Error(`\u4FDD\u5B58\u914D\u7F6E\u6587\u4EF6\u5931\u8D25: ${t instanceof Error?t.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async setPrdFilePath(t){if(!await e.pathExists(t))throw new Error(`PRD \u6587\u4EF6\u4E0D\u5B58\u5728: ${t}`);this.config.prdFilePath=t,await this.saveConfig()}async setOutputDirectory(t){await e.ensureDir(t),this.config.outputDirectory=t,await this.saveConfig()}getPrdFilePath(){return this.config.prdFilePath}getOutputDirectory(){return this.config.outputDirectory}async addRecentPrompt(t){this.config.lastUsedPrompts||(this.config.lastUsedPrompts=[]),this.config.lastUsedPrompts=this.config.lastUsedPrompts.filter(i=>i!==t),this.config.lastUsedPrompts.unshift(t),this.config.lastUsedPrompts=this.config.lastUsedPrompts.slice(0,10),await this.saveConfig()}getRecentPrompts(){return this.config.lastUsedPrompts||[]}async setUserPreference(t,i){this.config.userPreferences||(this.config.userPreferences={}),this.config.userPreferences[t]=i,await this.saveConfig()}getUserPreference(t){var i;return(i=this.config.userPreferences)==null?void 0:i[t]}isConfigComplete(){return!!(this.config.prdFilePath&&this.config.outputDirectory)}getConfigStatus(){return{hasPrdFile:!!this.config.prdFilePath,hasOutputDir:!!this.config.outputDirectory,prdFilePath:this.config.prdFilePath,outputDirectory:this.config.outputDirectory,isComplete:this.isConfigComplete()}}async resetConfig(){this.config={userPreferences:{language:"zh-CN",theme:"light"}},await this.saveConfig()}}export{o as ConfigManager};
