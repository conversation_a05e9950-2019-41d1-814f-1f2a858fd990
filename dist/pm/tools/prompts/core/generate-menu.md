## 生成Funi管理系统侧边栏菜单提示词

### 任务描述

根据提供的菜单数据，生成符合Funi管理系统`./framework/base-template.html`页面模板的侧边栏导航菜单HTML代码。生成的菜单应集成模板中已有的样式和JavaScript交互逻辑。

### 菜单数据

菜单数据应从用户提供的`PRD.md`文件的“系统功能菜单结构”中解析。请按照以下规则提取和构建菜单结构：

1.  **解析层级**: 识别一级菜单和二级菜单。
    - 一级菜单：直接在[系统名称]下的项。
    - 二级菜单：在一级菜单下通过缩进表示的项。
2.  **数据结构**: 将解析出的菜单转换为一个JSON数组，每个元素代表一个菜单项。
    - 每个菜单项应包含`title`（菜单标题）。
    - 如果菜单项有子菜单，则应包含`children`数组，其中包含子菜单项。
    - 对于没有子菜单的一级菜单和所有二级菜单，应包含`path`字段，表示其对应的路由路径。路径应为hash路由，即在原路径前添加`#`（例如，“工作台” -> `#/dashboard`，“项目标段管理” -> `#/procurement-execution/project-bid-management`）。
    - 对于一级菜单，请根据其标题自动生成一个符合Iconify规范的图标（例如，`mdi:view-dashboard` for "工作台"，`mdi:file-document-outline` for "采购计划管理"）。如果无法找到合适的图标，请使用一个通用的默认图标，例如`mdi:circle-medium`。

**示例菜单结构 (概念性，请根据PRD内容实际解析):**

```json
[
  {
    "title": "工作台",
    "icon": "mdi:view-dashboard",
    "path": "/dashboard"
  },
  {
    "title": "采购执行管理",
    "icon": "mdi:folder-open",
    "children": [
      {
        "title": "项目标段管理",
        "path": "/procurement-execution/project-bid-management"
      }
    ]
  }
]
```

### 样式和脚本引用

请确保生成的HTML菜单结构能够正确利用`base-template.html`中已引入的以下样式和脚本：

**CSS文件:**

- `assets/css/funi-framework.css`
- `assets/css/funi-components.css`
- `assets/css/funi-themes.css`

**JavaScript文件:**

- `assets/js/funi-theme-switcher.js`
- `assets/js/funi-interactions.js`

### HTML结构要求

请在`<nav class="funi-menu">`标签内生成菜单HTML。

**一级菜单 (无子菜单):**

```html
<ul class="funi-menu-list">
  <li class="funi-menu-item">
    <a href="{path}" class="funi-menu-link">
      <iconify-icon icon="{icon}" class="funi-menu-icon"></iconify-icon>
      <span class="funi-menu-text">{title}</span>
    </a>
  </li>
</ul>
```

**一级菜单 (有子菜单):**

```html
<div class="funi-menu-group" data-group-id="{group-id}">
  <div class="funi-menu-group-title">
    <div style="display: flex; align-items: center;">
      <!-- New wrapper div for icon and text -->
      <iconify-icon icon="{icon}" class="funi-menu-icon"></iconify-icon>
      <span>{title}</span>
    </div>
    <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
    </svg>
  </div>
  <ul class="funi-menu-list">
    <!-- 子菜单项 -->
    <li class="funi-menu-item">
      <a href="{child-path}" class="funi-menu-link">
        <span class="funi-menu-text">{child-title}</span>
      </a>
    </li>
    <!-- 更多子菜单项 -->
  </ul>
</div>
```

**图标说明:**

- 对于一级菜单（包括有子菜单的组标题），请根据菜单标题自动选择一个合适的Iconify图标，并使用`<iconify-icon icon="{icon}" class="funi-menu-icon"></iconify-icon>`。如果无法找到合适的图标，请使用默认图标`mdi:circle-medium`。
- 对于二级菜单，不需要图标。

**激活状态:**

- 当前页面对应的菜单项应添加`funi-menu-item-active`类。

### 交互逻辑

请确保生成的HTML菜单能够与`base-template.html`中已有的JavaScript交互逻辑兼容。

- 侧边栏的折叠/展开功能由`toggleSidebar()`函数控制。
- 主题切换功能由`toggleTheme()`函数控制。
- 菜单组的展开/折叠功能应通过点击`.funi-menu-group-title`来触发，并切换`.funi-menu-group`上的类来控制子菜单的显示/隐藏。
- 菜单链接的点击应更新主页面`index.html`的hash值，并通过`funi-router.js`脚本在iframe中加载对应的页面。
- **特别说明**: 当URL中没有hash值时，iframe的`src`应为空，且不应有任何菜单项被选中。

### 最终输出

请将生成的完整HTML菜单代码直接输出到用户指定目录中，文件名为`index.html`。
