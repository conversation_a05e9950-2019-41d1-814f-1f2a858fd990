# Funi原型系统使用说明

## 🚨 重要说明

由于浏览器安全策略限制，**直接双击打开index.html文件可能无法正常使用所有功能**（特别是页面跳转功能）。

**⚠️ 启动脚本使用说明**：
- 所有启动脚本都位于 `script/` 目录中
- 脚本会自动切换到项目根目录并启动服务器
- 确保 `index.html` 文件在项目根目录（与 `script/` 目录同级）

## 🎯 多种启动方式（任选其一）

### 方法1：一键启动脚本（推荐）⭐

#### Windows用户：
1. 进入 `script` 目录
2. 双击运行 `start-server.bat`
3. 服务器会自动启动并打开浏览器
4. 支持自动端口检测和Python环境检查

#### Mac/Linux用户：
1. 进入 `script` 目录
2. 双击运行 `start-server.sh`（或在终端中运行）
3. 或者运行：`./start-server.sh [端口号]`
4. 支持彩色输出和智能浏览器打开

#### 跨平台Python脚本：
```bash
cd script
python3 start-server.py [端口号]
```

### 方法2：Node.js启动（开发者推荐）🚀

#### 使用Node.js脚本：
```bash
cd script
node start-server.js [端口号]
```

#### 使用npm命令：
```bash
npm start          # 默认8080端口
npm run dev        # 开发模式，3000端口
npm run serve      # 同start
```

### 方法3：传统HTTP服务器

#### 使用Python内置服务器：
```bash
# 在项目根目录（html/）运行
# Python 3
python3 -m http.server 8080

# Python 2
python -m SimpleHTTPServer 8080
```

#### 使用Node.js工具：
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8080
```

#### 使用其他工具：
- **Live Server** (VS Code插件)
- **XAMPP/WAMP** 等本地服务器
- **Nginx** 静态文件服务

## 🌟 功能特性

### 核心功能
- ✅ 响应式布局，支持桌面端
- ✅ 深色/浅色主题切换
- ✅ 侧边栏折叠/展开
- ✅ 菜单导航和页面路由
- ✅ 列表、新增、编辑、详情页面
- ✅ 表单验证和数据处理
- ✅ 模拟数据和交互效果

### 启动脚本特性
- 🔍 自动检测可用端口
- 🌐 自动打开浏览器
- 🎨 彩色终端输出
- 🔄 支持热重载
- 🛡️ 优雅的错误处理
- 📱 跨平台支持

## 🔧 故障排除

### 问题1：点击菜单或详情按钮没有反应
**原因**：使用file://协议直接打开，浏览器阻止了跨域请求
**解决**：使用上述任一HTTP服务器方法

### 问题2：启动脚本无法运行
**可能原因**：
1. Python/Node.js未安装或版本过低
2. 脚本没有执行权限
3. 端口被占用

**解决方案**：
1. 安装Python 3.6+或Node.js 12+
2. Linux/Mac: `chmod +x start-server.sh`
3. 使用其他端口：`python3 start-server.py 3000`

### 问题3：页面显示"页面加载失败"
**可能原因**：
1. 页面文件不存在
2. 路径错误
3. 浏览器安全设置过严

**解决方案**：
1. 检查文件是否存在于正确路径
2. 使用HTTP服务器运行
3. 检查浏览器控制台错误信息

### 问题4：页面样式丢失或显示异常
**原因**：CSS文件加载失败或路径错误
**解决方案**：
1. 确认使用HTTP服务器运行（不要直接打开HTML文件）
2. 检查浏览器控制台是否有CSS加载错误
3. 确认`index.html`中包含所有必需的CSS文件
4. 参考`docs/pm/framework/CSS_LOADING_GUIDE.md`了解CSS加载机制

## 📁 目录结构

```
html/                           # 项目根目录
├── index.html                  # 系统入口页面
├── assets/                     # 静态资源目录
│   ├── FUNI.svg               # 项目Logo
│   ├── css/                   # 样式文件目录
│   │   ├── funi-components.css # 组件样式
│   │   ├── funi-form.css      # 表单样式
│   │   ├── funi-framework.css # 框架样式
│   │   ├── funi-list.css      # 列表样式
│   │   └── funi-themes.css    # 主题样式
│   └── js/                    # JavaScript文件目录
│       ├── funi-interactions.js # 交互逻辑
│       ├── funi-router.js     # 路由管理
│       └── funi-theme-switcher.js # 主题切换
├── pages/                     # 业务页面目录
└── script/                    # 启动脚本目录
    ├── README.md              # 脚本使用说明
    ├── start-server.bat       # Windows启动脚本
    ├── start-server.js        # Node.js启动脚本
    ├── start-server.py        # Python启动脚本
    └── start-server.sh        # Linux/Mac启动脚本
```


## 💡 开发说明

本原型系统基于原生HTML、CSS、JavaScript开发，无需额外依赖。所有页面都是根据提示词自动生成的静态页面，用于产品原型展示和需求验证。

---

**🎉 现在开始体验Funi原型系统吧！**
