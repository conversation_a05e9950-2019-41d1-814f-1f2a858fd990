import t from"fs-extra";import*as n from"path";import*as f from"os";import{createHash as D,randomUUID as l}from"crypto";class w{configDir;sessionsDir;sessionId;config;sessionInfo;constructor(s,i,e){this.configDir=n.join(f.homedir(),".funi-mcp"),this.sessionsDir=n.join(this.configDir,"sessions"),this.sessionId=s||this.generateSessionId(i,e),this.sessionInfo={sessionId:this.sessionId,userId:i,projectPath:e,createdAt:new Date().toISOString(),lastUsedAt:new Date().toISOString()},this.config={sessionId:this.sessionId,userPreferences:{language:"zh-CN",theme:"light"}}}generateSessionId(s,i){if(s&&i){const e=D("md5");return e.update(`${s}-${i}`),e.digest("hex").substring(0,16)}return l().replace(/-/g,"").substring(0,16)}getSessionConfigPath(){return n.join(this.sessionsDir,`${this.sessionId}.json`)}getSessionInfoPath(){return n.join(this.sessionsDir,`${this.sessionId}.info.json`)}async loadConfig(){try{await t.ensureDir(this.sessionsDir);const s=this.getSessionConfigPath(),i=this.getSessionInfoPath();if(await t.pathExists(s)){const e=await t.readFile(s,"utf-8");this.config={...this.config,...JSON.parse(e)}}if(await t.pathExists(i)){const e=await t.readFile(i,"utf-8");this.sessionInfo={...this.sessionInfo,...JSON.parse(e)}}return this.sessionInfo.lastUsedAt=new Date().toISOString(),await this.saveSessionInfo(),this.config}catch(s){throw new Error(`\u52A0\u8F7D\u4F1A\u8BDD\u914D\u7F6E\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async saveConfig(){try{await t.ensureDir(this.sessionsDir);const s=this.getSessionConfigPath();this.config.sessionId=this.sessionId,await t.writeFile(s,JSON.stringify(this.config,null,2),"utf-8"),await this.saveSessionInfo()}catch(s){throw new Error(`\u4FDD\u5B58\u4F1A\u8BDD\u914D\u7F6E\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}async saveSessionInfo(){const s=this.getSessionInfoPath();this.sessionInfo.lastUsedAt=new Date().toISOString(),await t.writeFile(s,JSON.stringify(this.sessionInfo,null,2),"utf-8")}async setPrdFilePath(s){if(!await t.pathExists(s))throw new Error(`PRD \u6587\u4EF6\u4E0D\u5B58\u5728: ${s}`);this.config.prdFilePath=s,await this.saveConfig()}async setOutputDirectory(s){await t.ensureDir(s),this.config.outputDirectory=s,await this.saveConfig()}getPrdFilePath(){return this.config.prdFilePath}getOutputDirectory(){return this.config.outputDirectory}getSessionId(){return this.sessionId}getSessionInfo(){return{...this.sessionInfo}}async addRecentPrompt(s){this.config.lastUsedPrompts||(this.config.lastUsedPrompts=[]),this.config.lastUsedPrompts=this.config.lastUsedPrompts.filter(i=>i!==s),this.config.lastUsedPrompts.unshift(s),this.config.lastUsedPrompts=this.config.lastUsedPrompts.slice(0,10),await this.saveConfig()}getRecentPrompts(){return this.config.lastUsedPrompts||[]}isConfigComplete(){return!!(this.config.prdFilePath&&this.config.outputDirectory)}getConfigStatus(){return{sessionId:this.sessionId,hasPrdFile:!!this.config.prdFilePath,hasOutputDir:!!this.config.outputDirectory,prdFilePath:this.config.prdFilePath,outputDirectory:this.config.outputDirectory,isComplete:this.isConfigComplete(),sessionInfo:this.getSessionInfo()}}static async cleanupExpiredSessions(s=24){try{const i=n.join(f.homedir(),".funi-mcp"),e=n.join(i,"sessions");if(!await t.pathExists(e))return 0;const h=(await t.readdir(e)).filter(r=>r.endsWith(".info.json"));let o=0;const a=new Date(Date.now()-s*60*60*1e3);for(const r of h){const u=n.join(e,r),g=r.replace(".info.json","");try{const d=await t.readFile(u,"utf-8"),p=JSON.parse(d);if(new Date(p.lastUsedAt)<a){const c=n.join(e,`${g}.json`);await t.remove(u),await t.pathExists(c)&&await t.remove(c),o++}}catch{await t.remove(u),o++}}return o}catch(i){throw new Error(`\u6E05\u7406\u8FC7\u671F\u4F1A\u8BDD\u5931\u8D25: ${i instanceof Error?i.message:"\u672A\u77E5\u9519\u8BEF"}`)}}static async listActiveSessions(){try{const s=n.join(f.homedir(),".funi-mcp"),i=n.join(s,"sessions");if(!await t.pathExists(i))return[];const e=(await t.readdir(i)).filter(o=>o.endsWith(".info.json")),h=[];for(const o of e)try{const a=n.join(i,o),r=await t.readFile(a,"utf-8"),u=JSON.parse(r);h.push(u)}catch{}return h.sort((o,a)=>new Date(a.lastUsedAt).getTime()-new Date(o.lastUsedAt).getTime())}catch(s){throw new Error(`\u5217\u51FA\u6D3B\u8DC3\u4F1A\u8BDD\u5931\u8D25: ${s instanceof Error?s.message:"\u672A\u77E5\u9519\u8BEF"}`)}}}export{w as SessionConfigManager};
