import{SessionConfigManager as d}from"./session-config-manager.mjs";import{createHash as p}from"crypto";import*as c from"path";import*as r from"os";import a from"fs-extra";class S{static sessionCache=new Map;static processSessionsPath=c.join(r.homedir(),".funi-mcp","process-sessions.json");static lockPath=c.join(r.homedir(),".funi-mcp","process-sessions.lock");static isWriting=!1;static async getSessionManager(s){const e=await this.generateSessionKey(s);if(this.sessionCache.has(e)){const u=this.sessionCache.get(e);await u.loadConfig();const l=await this.getProcessSessionInfo(e);return{manager:u,sessionInfo:l}}const{userId:t,projectPath:i,sessionId:o}=this.extractSessionInfo(s),n=new d(o,t,i);await n.loadConfig(),this.sessionCache.set(e,n);const h=await this.registerProcessSession(e,{sessionId:n.getSessionId(),userId:t,projectPath:i},s==null?void 0:s.processId);return{manager:n,sessionInfo:h}}static async generateSessionKey(s){const e=(s==null?void 0:s.workingDirectory)||process.cwd(),t=(s==null?void 0:s.userName)||r.userInfo().username,i=(s==null?void 0:s.processId)||process.pid;return s!=null&&s.sessionId?`manual-${s.sessionId}-${i}`:p("md5").update(`${t}-${e}-${i}-${Date.now()}-${Math.random()}`).digest("hex").substring(0,16)}static extractSessionInfo(s){const e=(s==null?void 0:s.workingDirectory)||process.cwd();return{userId:`${(s==null?void 0:s.userName)||r.userInfo().username}@${r.hostname()}`,projectPath:e,sessionId:s==null?void 0:s.sessionId}}static async registerProcessSession(s,e,t){const i=await this.loadProcessSessions(),o=t||process.pid,n={sessionId:e.sessionId,processId:o,userId:e.userId,projectPath:e.projectPath,createdAt:new Date().toISOString(),lastUsedAt:new Date().toISOString(),isActive:!0};return i[s]=n,await this.saveProcessSessions(i),n}static async getProcessSessionInfo(s){const e=await this.loadProcessSessions(),t=e[s];return t&&(t.lastUsedAt=new Date().toISOString(),await this.saveProcessSessions(e)),t}static async getActiveProjectSessions(s,e){const t=await this.loadProcessSessions(),i=`${e}@${r.hostname()}`;return Object.values(t).filter(o=>o.projectPath===s&&o.userId===i&&o.isActive&&this.isProcessAlive(o.processId))}static isProcessAlive(s){try{return process.kill(s,0),!0}catch{return!1}}static async loadProcessSessions(){try{if(await a.pathExists(this.processSessionsPath)){const s=await a.readFile(this.processSessionsPath,"utf-8");return JSON.parse(s)}}catch(s){console.warn("\u52A0\u8F7D\u8FDB\u7A0B\u4F1A\u8BDD\u6570\u636E\u5931\u8D25:",s)}return{}}static async saveProcessSessions(s){for(;this.isWriting;)await new Promise(e=>setTimeout(e,10));this.isWriting=!0;try{await a.ensureDir(c.dirname(this.processSessionsPath));const e=this.processSessionsPath+".tmp";await a.writeFile(e,JSON.stringify(s,null,2),"utf-8"),await a.move(e,this.processSessionsPath,{overwrite:!0})}catch(e){console.warn("\u4FDD\u5B58\u8FDB\u7A0B\u4F1A\u8BDD\u6570\u636E\u5931\u8D25:",e)}finally{this.isWriting=!1}}static async cleanupDeadProcessSessions(){const s=await this.loadProcessSessions();let e=0;for(const[t,i]of Object.entries(s))this.isProcessAlive(i.processId)||(delete s[t],e++);return e>0&&await this.saveProcessSessions(s),e}static async listActiveProcessSessions(){const s=await this.loadProcessSessions();return Object.values(s).filter(e=>e.isActive&&this.isProcessAlive(e.processId))}static async getProjectSessionStats(){const s=await this.listActiveProcessSessions(),e=new Map;for(const t of s){const i=`${t.userId}:${t.projectPath}`;e.has(i)||e.set(i,[]),e.get(i).push(t)}return Array.from(e.entries()).map(([t,i])=>{const[o,n]=t.split(":");return{projectPath:n,userId:o,sessionCount:i.length,sessions:i}})}static async detectProjectInfo(s){const e=(s==null?void 0:s.workingDirectory)||process.cwd(),t=(s==null?void 0:s.userName)||r.userInfo().username,i=(s==null?void 0:s.projectName)||c.basename(e),o=await this.generateSessionKey(s),n=await this.getActiveProjectSessions(e,t);return{userId:`${t}@${r.hostname()}`,projectPath:e,projectName:i,sessionKey:o,hasMultipleSessions:n.length>0,sessionCount:n.length}}static clearCache(){this.sessionCache.clear()}static getCacheStatus(){const s=Array.from(this.sessionCache.entries()).map(([e,t])=>({sessionKey:e,sessionId:t.getSessionId(),userId:t.getSessionInfo().userId,projectPath:t.getSessionInfo().projectPath}));return{cacheSize:this.sessionCache.size,sessions:s}}}export{S as EnhancedSessionManager};
