{"name": "FuniA0Mcp", "type": "module", "version": "0.0.1", "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab", "description": "Funi Ai Mcp Server", "contributors": [{"name": "qian.luo", "email": "<EMAIL>"}], "license": "MIT", "exports": {".": "./dist/index.mjs", "./cli": "./bin/cli.mjs"}, "bin": {"mcp-instruct": "./bin/cli.mjs"}, "files": ["bin", "dist"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "unbuild && npm run chmod-run", "chmod-run": "node -e \"fs.chmodSync('dist/index.mjs', '755'); if (require('fs').existsSync('dist/cli.mjs')) require('fs').chmodSync('dist/cli.mjs', '755');\"", "start": "nodemon --exec 'tsx src/index.ts'", "dev:prepare": "nr build", "inspect": "npx @modelcontextprotocol/inspector@latest", "dev": "npx concurrently 'unbuild --stub' 'npm run inspect'", "run-cli": "node bin/cli.mjs", "dev-stdio": "npx concurrently 'npm run run-cli' 'npm run inspect node ./bin/cli.mjs'", "dev-http": "npx concurrently 'npm run run-cli -- --http --port 4200' 'npm run inspect http://localhost:4200/mcp'", "dev-sse": "npx concurrently 'npm run run-cli -- --sse --port 4201' 'npm run inspect http://localhost:4201/sse'", "lint": "eslint", "lint:fix": "eslint --fix", "typecheck": "tsc --noEmit", "test": "vitest", "release": "tsx scripts/release.ts"}, "dependencies": {"@chatmcp/sdk": "^1.0.6", "@modelcontextprotocol/sdk": "^1.9.0", "citty": "^0.1.6", "fs-extra": "^11.3.0", "gray-matter": "^4.0.3", "h3": "^1.15.1", "ofetch": "^1.4.1", "zod": "^3.24.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.14.1", "dotenv": "^16.5.0", "esbuild": "^0.25.2", "nodemon": "^3.1.9", "tsx": "^4.19.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vite": "^6.3.1", "vitest": "^3.1.1"}}