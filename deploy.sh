#!/bin/bash

# 部署脚本 - 将MCP服务部署到测试环境

set -e

echo "🚀 开始部署 webMcp 服务..."

# 构建Docker镜像
echo "📦 构建Docker镜像..."
docker build -t webmcp:latest .

# 可选：推送到镜像仓库（根据你的实际情况修改）
# echo "📤 推送镜像到仓库..."
# docker tag webmcp:latest your-registry/webmcp:latest
# docker push your-registry/webmcp:latest

echo "✅ 部署完成！"
echo ""
echo "🔧 Trae IDE 配置更新："
echo "将以下配置添加到你的 Trae IDE MCP 设置中："
echo ""
echo '{'
echo '  "mcpServers": {'
echo '    "FuniA0Mcp": {'
echo '      "transport": {'
echo '        "type": "http",'
echo '        "baseUrl": "http://bpaas.funi.com/test/webMcp"'
echo '      }'
echo '    }'
echo '  }'
echo '}'
echo ""
echo "⚠️  注意："
echo "1. 确保服务部署后在 http://bpaas.funi.com/test/webMcp 可访问"
echo "2. 服务默认监听端口 3000，endpoint 为 /mcp"
echo "3. 完整的MCP端点URL应该是: http://bpaas.funi.com/test/webMcp/mcp"
