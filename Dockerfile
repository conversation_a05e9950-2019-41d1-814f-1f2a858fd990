FROM docker.funi.local:5000/node:latest

# 设置工作目录
WORKDIR /opt

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production

# 复制构建后的文件，保持原有的目录结构
COPY dist/ /opt/dist/
COPY bin/ /opt/bin/

# 设置执行权限
RUN chmod +x /opt/dist/index.mjs /opt/bin/cli.mjs

# 调试：查看文件结构和内容
RUN echo "=== /opt 目录内容 ===" && ls -la /opt/
RUN echo "=== dist 目录内容 ===" && ls -la /opt/dist/
RUN echo "=== bin 目录内容 ===" && ls -la /opt/bin/

# 暴露端口
EXPOSE 10101

# 启动应用 - 使用HTTP模式，适合容器环境
ENTRYPOINT ["node", "/opt/bin/cli.mjs", "--http", "--port", "10101"]
